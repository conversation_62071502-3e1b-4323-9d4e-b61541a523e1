package com.ygxj.self.utils

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream
import timber.log.Timber
import java.io.BufferedInputStream
import java.io.File
import java.io.FileOutputStream


object AssetsUnzipUtils {

    /**
     * 解压assets目录下的zip文件到app私有目录
     * @param context 上下文
     * @param assetFileName assets中的zip文件名，如"self.zip"
     * @return 解压是否成功
     */
    suspend fun unzipAssetsFile(
        context: Context,
        assetFileName: String = "self.zip",
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            context.assets.open(assetFileName).use { inputStream ->
                val zipInputStream = ZipArchiveInputStream(BufferedInputStream(inputStream))

                var entry: ZipArchiveEntry?
                val buffer = ByteArray(8192)

                while (zipInputStream.nextZipEntry.also { entry = it } != null) {
                    val entryFile = File(context.getExternalFilesDir(""), entry!!.name)
                    Timber.d("解压文件: ${entry.name}")

                    if (entry.isDirectory) {
                        entryFile.mkdirs()
                    } else {
                        entryFile.parentFile?.mkdirs()
                        FileOutputStream(entryFile).use { outputStream ->
                            var len: Int
                            while (zipInputStream.read(buffer).also { len = it } != -1) {
                                outputStream.write(buffer, 0, len)
                            }
                        }
                    }
                }
            }
            true
        } catch (e: Exception) {
            Timber.e(e, "解压失败")
            false
        }
    }
}