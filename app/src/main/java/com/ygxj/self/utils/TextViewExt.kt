package com.ygxj.self.utils


import android.graphics.Paint
import android.text.SpannableString
import android.text.Spanned
import android.text.style.LeadingMarginSpan
import android.widget.TextView


/**
 * 设置首行缩进两个字符
 */
fun TextView.setFirstLineIndent(text: String) {
    val spannableString = SpannableString(text)

    val paint = Paint()
    paint.textSize = textSize
    val indentWidth = paint.measureText("汉") * 2 // 汉字的宽度乘以缩进的字符数

    val span: LeadingMarginSpan = object : LeadingMarginSpan.Standard(indentWidth.toInt(), 0) {
        override fun getLeadingMargin(first: Boolean): Int {
            return if (first) {
                super.getLeadingMargin(true)
            } else {
                0
            }
        }
    }

    spannableString.setSpan(span, 0, spannableString.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)

    this.text = spannableString
}

