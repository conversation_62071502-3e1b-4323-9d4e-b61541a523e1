package com.ygxj.self.utils

import android.annotation.SuppressLint
import android.net.Uri
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageView
import androidx.databinding.BindingAdapter
import com.bumptech.glide.Glide
import com.ygxj.self.R

@BindingAdapter("loadImage")
fun ImageView.loadImage(src: String?) {
    Glide.with(context).load(if (src.isNullOrBlank()) R.drawable.placeholder else "${src}")
        .placeholder(R.drawable.placeholder)
        .error(R.drawable.placeholder)
        .into(this)
}

@BindingAdapter("loadImage")
fun ImageView.loadImage(src: Int?) {
    Glide.with(context).load(src)
        .placeholder(R.drawable.placeholder)
        .error(R.drawable.placeholder)
        .into(this)
}


@BindingAdapter("loadUri")
fun ImageView.loadUri(uri: Uri?) {
    Glide.with(context).load(uri ?: R.drawable.placeholder)
        .placeholder(R.drawable.placeholder)
        .error(R.drawable.placeholder)
        .into(this)
}

@BindingAdapter("loadHtml")
fun WebView.loadHtml(content: String?) {
    this.webViewClient = MyWebViewClient()
    this.loadDataWithBaseURL(
        null,
        if (content.isNullOrBlank()) "暂无数据" else content,
        "text/html",
        "utf-8",
        null
    )
}

@SuppressLint("SetJavaScriptEnabled")
class MyWebViewClient : WebViewClient() {
    override fun onPageFinished(view: WebView, url: String) {
        view.settings.javaScriptEnabled = true
        super.onPageFinished(view, url)
        resetImgSize(view)
    }


    //设置图片最大宽度为100%
    private fun resetImgSize(webView: WebView) {
        webView.loadUrl(
            "javascript:(function(){"
                    + "var objs = document.getElementsByTagName(\"img\"); "
                    + "for(var i=0;i<objs.length;i++) "
                    + "{" //通过js代码找到标签为img的代码块，设置图片最大宽度
                    + "objs[i].style.maxWidth = '100%';objs[i].style.height = 'auto';"
                    + "}"
                    + "})()"
        )
    }
}