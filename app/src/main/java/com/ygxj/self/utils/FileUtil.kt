package com.ygxj.self.utils


import android.media.MediaMetadataRetriever


object FileUtil {

    /**
     * 获取音频时长毫秒值
     * @param path 音频路径
     * @return
     */
    fun getLocalVideoDuration(path: String): String{
        var duration = 0
        try {
            val mRetriever = MediaMetadataRetriever()
            mRetriever.setDataSource(path)
            duration = Integer.parseInt(mRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION))
            //时长(毫秒)
        } catch (e: Throwable) {
            e.printStackTrace()
            return "00:00"
        }

        return TimeUtil.formatMillionSeconds(duration.toLong())
    }

}