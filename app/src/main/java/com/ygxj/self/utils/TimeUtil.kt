package com.ygxj.self.utils

import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

object TimeUtil {

    /**
     * 根据秒数,返回转换后的时:分:秒
     */
    fun getHourMinSecFromSec(second: Int): String {
        val hour = second / 3600
        var min = second % 3600
        val sec = min % 60
        min /= 60
        return String.format("%02d:%02d:%02d", hour, min, sec)
    }

    /**
     * 根据秒数,返回转换后的分:秒
     */
    fun getMinSecFromSec(second: Long): String {
        val min = second / 60
        val sec = second % 60
        return String.format("%02d:%02d", min, sec)
    }


    /**
     * 将毫秒值转换为mm:ss
     */
    fun formatMillionSeconds(millionSeconds: Long): String {
        val simpleDateFormat = SimpleDateFormat("mm:ss", Locale.CHINESE)
        val c: Calendar = Calendar.getInstance()
        c.timeInMillis = millionSeconds
        return simpleDateFormat.format(c.time)
    }

    /**
     * 将mm:ss转换为毫秒值
     */
    fun getMillionSeconds(formatStr: String): Long {
        val minute = formatStr.substringBefore(":").toInt()
        val second = formatStr.substringAfter(":").toInt()
        return (minute * 60 + second) * 1000L
    }

    /**
     * 将秒转换为HH:mm:ss
     */
    fun formatSeconds(second: Int): String {
        val h: Int = second / 3600
        val m: Int = second % 3600 / 60
        val s: Int = second % 3600 % 60

        val hourText = if (h < 10) "0${h}" else "$h"
        val minuteText = if (m < 10) "0${m}" else "$m"
        val secondText = if (s < 10) "0${s}" else "$s"
        return "$hourText:$minuteText:$secondText"
    }

    /**
     * 把13位的时间戳转成 时分秒
     */
    fun convertTimestampToHMS(timestamp: Long?): String {
        if (timestamp==null){
            return ""
        }
        val date = Date(timestamp)
        val format = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
        return format.format(date)
    }
}