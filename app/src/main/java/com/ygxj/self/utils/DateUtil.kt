package com.ygxj.self.utils

import android.annotation.SuppressLint
import java.text.SimpleDateFormat
import java.util.Calendar

object DateUtil {

    /**
     * 获取当前时间
     */
    @SuppressLint("SimpleDateFormat")
    fun getCurrentDateTime(): String {
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        return dateFormat.format(calendar.time)
    }

    /**
     * 获取当前日期
     */
    @SuppressLint("SimpleDateFormat")
    fun getCurrentDate(): String {
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd")
        return dateFormat.format(calendar.time)
    }

    /**
     * 将时间戳转换为年月日时分秒格式
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     */
    @SuppressLint("SimpleDateFormat")
    fun timestampToDateTime(timestamp: Long): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        val calendar = Calendar.getInstance().apply { timeInMillis = timestamp }
        return dateFormat.format(calendar.time)
    }

    /**
     * 将时间戳转换为年月日时分秒格式
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     */
    @SuppressLint("SimpleDateFormat")
    fun timestampToDateTime2(timestamp: Long): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd")
        val calendar = Calendar.getInstance().apply { timeInMillis = timestamp }
        return dateFormat.format(calendar.time)
    }

    /**
     * 格式化日期
     */
    fun formatToDate(year: Int, month: Int, day: Int): String {
        return String.format("%04d-%02d-%02d", year, month, day)
    }

    /**
     * 将日期字符串转换为时间戳
     *
     * @param dateStr 日期字符串，格式为 "yyyy-MM-dd"
     * @return 时间戳（毫秒）
     */
    @SuppressLint("SimpleDateFormat")
    fun dateToTimestamp(dateStr: String): Long {
        return try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd")
            val date = dateFormat.parse(dateStr)
            date?.time ?: 0L
        } catch (e: Exception) {
            0L
        }
    }
}