package com.ygxj.self.ui.titbit


import android.os.Bundle
import android.view.View
import com.ygxj.self.R
import com.ygxj.self.data.entity.ImageEntity
import com.ygxj.self.data.repository.ImageRepository
import com.ygxj.self.data.repository.TitbitRepository
import com.ygxj.self.databinding.FragmentCopyBinding
import com.ygxj.self.databinding.FragmentTitbitDetailBinding
import com.ygxj.self.ui.base.BaseFragment
import com.ygxj.self.ui.image.ImagePreviewActivity
import com.ygxj.self.ui.main.MainActivity
import com.ygxj.self.ui.soul.movie.MovieDetailActivity
import com.ygxj.self.utils.setFirstLineIndent
import com.ygxj.self.utils.show
import com.youth.banner.indicator.CircleIndicator
import com.youth.banner.transformer.DepthPageTransformer

/**
 * 中心活动花絮详情
 */
class TitbitDetailFragment : BaseFragment<MainActivity, FragmentTitbitDetailBinding>() {

    private val mTitbitId by lazy { arguments?.getLong("titbitId") ?: 0L }

    private val mTitbitEntity by lazy { TitbitRepository.getCenterTitbitById(mTitbitId) }

    companion object {
        fun newInstance(titbitId: Long): TitbitDetailFragment {
            val f = TitbitDetailFragment()
            Bundle().also {
                it.putLong("titbitId", titbitId)
                f.arguments = it
            }
            return f
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_titbit_detail
    }

    override fun initView() {
        initBanner()
        binding.banner.show(mTitbitEntity.images.isNotEmpty())

        binding.tvContent.setFirstLineIndent(mTitbitEntity.content)
        binding.banner.setDatas(mTitbitEntity.images)
    }

    override fun initData() {

    }

    /**
     * 初始化轮播图
     */
    private fun initBanner() {
        //轮播图
        binding.banner.setAdapter(TitbitBannerAdapter())
            .setIndicator(CircleIndicator(requireContext()))
            .addBannerLifecycleObserver(this)
            .setOnBannerListener { data, position ->
                binding.banner.stop()
                val image = data as ImageEntity
                if (image.type==2){
                    previewVideo(image.path)
                }else{
                    previewPicture(image.path)
                }
            }.setIntercept(false)
    }

    /**
     * 预览视频
     */
    private fun previewVideo(path:String) {
        MovieDetailActivity.start(requireContext(), path)
    }

    /**
     * 预览图片
     */
    private fun previewPicture(path:String) {
        ImagePreviewActivity.start(requireContext(), listOf(path))
    }

    override fun onResume() {
        super.onResume()
        binding.banner.start()
    }

}