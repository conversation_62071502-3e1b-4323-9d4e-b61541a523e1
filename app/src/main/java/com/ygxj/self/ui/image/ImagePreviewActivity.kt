package com.ygxj.self.ui.image

import android.content.Context
import android.content.Intent
import androidx.viewpager2.widget.ViewPager2
import com.ygxj.self.R
import com.ygxj.self.databinding.ActivityImagePreviewBinding
import com.ygxj.self.ui.base.BaseActivity

/**
 * 图片预览
 */
class ImagePreviewActivity : BaseActivity<ActivityImagePreviewBinding>() {
    //图片路径
    private val paths by lazy {
        intent.getStringArrayListExtra("paths")?.toList() ?: emptyList()
    }

    //图片标题
    private val titles by lazy {
        intent.getStringArrayListExtra("titles")?.toList() ?: emptyList()
    }

    //默认显示的图片索引
    private val index by lazy {
        intent.getIntExtra("index", 0)
    }

    companion object {
        fun start(
            context: Context,
            paths: List<String>,
            titles: List<String> = emptyList(),
            index: Int = 0
        ) {
            val intent = Intent(context, ImagePreviewActivity::class.java)
            intent.putStringArrayListExtra("paths", ArrayList(paths))
            intent.putStringArrayListExtra("titles", ArrayList(titles))
            intent.putExtra("index", index)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId() = R.layout.activity_image_preview

    override fun initView() {
        initViewPager2()
    }

    override fun initData() {

    }

    private fun initViewPager2() {
        binding.viewPager2.apply {
            adapter = ImagePreviewAdapter(paths, titles) {
                finish()
            }
            orientation = ViewPager2.ORIENTATION_HORIZONTAL
            setCurrentItem(index, false)
        }
    }

}