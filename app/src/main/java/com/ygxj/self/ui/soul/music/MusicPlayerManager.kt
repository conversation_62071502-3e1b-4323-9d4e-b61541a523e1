package com.ygxj.self.ui.soul.music

import android.content.Context
import android.os.Handler
import android.os.Looper
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import com.ygxj.self.data.entity.RelaxEntity
import timber.log.Timber
import kotlin.collections.forEach
import kotlin.let
import kotlin.random.Random

/**
 * 音乐播放器管理器
 */
object MusicPlayerManager {
    private var mMusicPlayer: ExoPlayer? = null //播放器
    private var mMusicList = listOf<RelaxEntity>() //音乐列表
    private var mCurrentIndex = 0 //当前播放的音乐索引
    private var mPlayMode = PlayMode.SEQUENCE //播放模式
    private var mPlaybackListener: PlaybackListener? = null //播放回调
    private var mProgressHandler = Handler(Looper.getMainLooper()) //进度更新handler
    private var mProgressRunnable: Runnable? = null //进度更新runnable

    enum class PlayMode {
        SEQUENCE,    // 顺序播放
        RANDOM,      // 随机播放
        REPEAT_ONE   // 单曲循环
    }

    interface PlaybackListener {
        //播放状态改变
        fun onPlayStateChanged(isPlaying: Boolean)

        //音乐改变
        fun onMusicChanged(relaxEntity: RelaxEntity, index: Int)

        //播放进度改变
        fun onProgressChanged(position: Long, duration: Long)

        //播放模式改变
        fun onPlayModeChanged(playMode: PlayMode)
    }

    //初始化播放器
    fun initPlayer(context: Context) {
        if (mMusicPlayer == null) {
            mMusicPlayer = ExoPlayer.Builder(context).build()
            mMusicPlayer?.addListener(object : Player.Listener {
                override fun onIsPlayingChanged(isPlaying: Boolean) {
                    mPlaybackListener?.onPlayStateChanged(isPlaying)
                    // 开始或停止进度更新
                    if (isPlaying) {
                        startProgressUpdate()
                    } else {
                        stopProgressUpdate()
                    }
                }

                override fun onPlaybackStateChanged(playbackState: Int) {
                    when (playbackState) {
                        Player.STATE_ENDED -> {
                            Timber.e("播放器播放完成了")
                            if (mPlayMode != PlayMode.REPEAT_ONE) {
                                playNext()
                            }
                        }

                        Player.STATE_READY -> {
                            // 播放器准备就绪，可以获取时长
                            notifyProgressChanged()
                            Timber.e("播放器准备就绪")
                        }

                        Player.STATE_IDLE -> {
                            Timber.e("播放器空闲中")
                        }
                        Player.STATE_BUFFERING -> {
                            Timber.e("播放器缓冲中")
                        }

                        else -> {
                            // 其他状态，如缓冲中、错误等
                            Timber.e("播放器其他状态")
                        }
                    }
                }
            })
        }
    }

    //设置音乐列表
    fun setMusicList(musicList: List<RelaxEntity>, startIndex: Int = 0) {
        mMusicList = musicList
        mCurrentIndex = startIndex
        setupPlaylist()
    }

    //设置播放列表
    private fun setupPlaylist() {
        mMusicPlayer?.clearMediaItems()
        mMusicList.forEach { music ->
            val mediaItem = MediaItem.fromUri(music.path)
            mMusicPlayer?.addMediaItem(mediaItem)
        }
        mMusicPlayer?.prepare()
        mMusicPlayer?.seekToDefaultPosition(mCurrentIndex)

        // 设置播放模式
        updatePlayerRepeatMode()
    }

    //更新播放器的循环模式
    private fun updatePlayerRepeatMode() {
        when (mPlayMode) {
            PlayMode.REPEAT_ONE -> {
                mMusicPlayer?.repeatMode = Player.REPEAT_MODE_ONE
            }

            else -> {
                mMusicPlayer?.repeatMode = Player.REPEAT_MODE_OFF
            }
        }
    }

    //播放
    fun play() {
        mMusicPlayer?.playWhenReady = true
        notifyMusicChanged()
    }

    //暂停
    fun pause() {
        mMusicPlayer?.playWhenReady = false
    }

    //切换播放/暂停
    fun togglePlayPause() {
        if (isPlaying()) {
            pause()
        } else {
            play()
        }
    }

    //播放下一首
    fun playNext() {
        val nextIndex = getNextIndex()
        if (nextIndex != mCurrentIndex) {
            mCurrentIndex = nextIndex
            mMusicPlayer?.seekToDefaultPosition(mCurrentIndex)
            play()
        }
    }

    //播放上一首
    fun playPrevious() {
        val prevIndex = getPreviousIndex()
        if (prevIndex != mCurrentIndex) {
            mCurrentIndex = prevIndex
            mMusicPlayer?.seekToDefaultPosition(mCurrentIndex)
            play()
        }
    }

    //播放指定索引的音乐
    fun playAtIndex(index: Int) {
        if (index in mMusicList.indices) {
            mCurrentIndex = index
            mMusicPlayer?.seekToDefaultPosition(mCurrentIndex)
            play()
        }
    }

    //获取下一首的索引
    private fun getNextIndex(): Int {
        return when (mPlayMode) {
            PlayMode.SEQUENCE -> {
                if (mCurrentIndex < mMusicList.size - 1) {
                    mCurrentIndex + 1
                } else {
                    0 // 循环到第一首
                }
            }

            PlayMode.RANDOM -> {
                if (mMusicList.size <= 1) {
                    mCurrentIndex
                } else {
                    var randomIndex: Int
                    do {
                        randomIndex = Random.nextInt(mMusicList.size)
                    } while (randomIndex == mCurrentIndex) // 确保不重复当前歌曲
                    randomIndex
                }
            }

            PlayMode.REPEAT_ONE -> mCurrentIndex
        }
    }

    //获取上一首的索引
    private fun getPreviousIndex(): Int {
        return when (mPlayMode) {
            PlayMode.SEQUENCE -> {
                if (mCurrentIndex > 0) {
                    mCurrentIndex - 1
                } else {
                    mMusicList.size - 1 // 循环到最后一首
                }
            }

            PlayMode.RANDOM -> {
                if (mMusicList.size <= 1) {
                    mCurrentIndex
                } else {
                    var randomIndex: Int
                    do {
                        randomIndex = Random.nextInt(mMusicList.size)
                    } while (randomIndex == mCurrentIndex)
                    randomIndex
                }
            }

            PlayMode.REPEAT_ONE -> mCurrentIndex
        }
    }

    //设置播放模式
    fun setPlayMode(playMode: PlayMode) {
        mPlayMode = playMode
        updatePlayerRepeatMode()
        mPlaybackListener?.onPlayModeChanged(playMode)
    }

    //获取播放模式
    fun getPlayMode(): PlayMode = mPlayMode

    //切换播放模式
    fun switchPlayMode(): PlayMode {
        val newMode = when (mPlayMode) {
            PlayMode.SEQUENCE -> PlayMode.RANDOM
            PlayMode.RANDOM -> PlayMode.REPEAT_ONE
            PlayMode.REPEAT_ONE -> PlayMode.SEQUENCE
        }
        setPlayMode(newMode)
        return newMode
    }

    //停止播放
    fun stopMusic() {
        mMusicPlayer?.stop()
        mMusicPlayer?.playWhenReady = false
        stopProgressUpdate()
    }

    //是否正在播放
    fun isPlaying(): Boolean = mMusicPlayer?.isPlaying ?: false

    //获取当前播放进度
    fun getCurrentPosition(): Long = mMusicPlayer?.currentPosition ?: 0L

    //获取当前音乐的总时长
    fun getDuration(): Long = mMusicPlayer?.duration ?: 0L

    //跳转到指定位置
    fun seekTo(position: Long) {
        mMusicPlayer?.seekTo(position)
    }

    //获取当前播放的音乐
    fun getCurrentMusic(): RelaxEntity? {
        return if (mCurrentIndex < mMusicList.size) {
            mMusicList[mCurrentIndex]
        } else null
    }

    //获取当前播放的音乐索引
    fun getCurrentIndex(): Int = mCurrentIndex

    //设置播放回调
    fun setPlaybackListener(listener: PlaybackListener?) {
        mPlaybackListener = listener
    }

    //通知音乐改变
    private fun notifyMusicChanged() {
        getCurrentMusic()?.let { music ->
            mPlaybackListener?.onMusicChanged(music, mCurrentIndex)
        }
    }

    //开始进度更新
    private fun startProgressUpdate() {
        stopProgressUpdate() // 先停止之前的更新
        mProgressRunnable = object : Runnable {
            override fun run() {
                notifyProgressChanged()
                mProgressHandler.postDelayed(this, 1000) // 每秒更新一次
            }
        }
        mProgressHandler.post(mProgressRunnable!!)
    }

    //停止进度更新
    private fun stopProgressUpdate() {
        mProgressRunnable?.let {
            mProgressHandler.removeCallbacks(it)
            mProgressRunnable = null
        }
    }

    //通知进度改变
    private fun notifyProgressChanged() {
        val position = getCurrentPosition()
        val duration = getDuration()
        mPlaybackListener?.onProgressChanged(position, duration)
    }

    //释放播放器
    fun release() {
        stopProgressUpdate()
        mMusicPlayer?.release()
        mMusicPlayer = null
    }
}