package com.ygxj.self.ui.soul.image

import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.ygxj.self.R
import com.ygxj.self.databinding.ItemImagePreviewBinding
import com.ygxj.self.utils.show

class ImageDetailAdapter(
    private val images: List<String?>,
    private val titles: List<String?> = emptyList(),
    private val onItemClick: () -> Unit
) :
    RecyclerView.Adapter<ImageDetailAdapter.ViewHolder>() {

    class ViewHolder(val binding: ItemImagePreviewBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemImagePreviewBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val bind = holder.binding
        val image = images[position]
        if (titles.isNotEmpty()) {
            bind.tvTitle.text = titles[position]
        }
        bind.tvNum.show(images.size>1)
        bind.tvNum.text = "${position + 1}/${images.size}"

        Glide.with(holder.itemView.context)
            .load("$image")
            .centerCrop()
            .error(R.drawable.placeholder)
            .listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable?>,
                    isFirstResource: Boolean
                ): Boolean {
                    bind.progress.visibility = View.GONE
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable?>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    bind.progress.visibility = View.GONE
                    return false
                }
            })
            .thumbnail(0.1f) // <- 这个方法已被弃用
            .into(bind.photoView)

        bind.photoView.setOnClickListener {
            // 关闭页面逻辑
            onItemClick()
        }
    }

    override fun getItemCount() = images.size
}