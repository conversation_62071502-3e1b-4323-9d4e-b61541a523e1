package com.ygxj.self.ui.center


import android.content.Context
import android.content.Intent
import android.view.View
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.ygxj.self.R
import com.ygxj.self.data.entity.ImageEntity
import com.ygxj.self.data.repository.CenterRepository
import com.ygxj.self.databinding.ActivityCenterIntroBinding
import com.ygxj.self.ui.base.BaseActivity
import com.ygxj.self.ui.image.ImagePreviewActivity
import com.ygxj.self.ui.soul.movie.MovieDetailActivity
import com.ygxj.self.ui.titbit.TitbitBannerAdapter
import com.ygxj.self.utils.setFirstLineIndent
import com.youth.banner.indicator.CircleIndicator
import com.youth.banner.transformer.DepthPageTransformer


/**
 * 中心介绍
 */
class CenterIntroActivity : BaseActivity<ActivityCenterIntroBinding>() {

    /**预览相关数据**/
    private val mTitle by lazy { intent.getStringExtra("title") }
    private val mContent by lazy { intent.getStringExtra("content") }
    private val mImages by lazy { intent.getSerializableExtra("images") as ArrayList<ImageEntity> }

    companion object {
        fun start(
            context: Context,
            title: String,
            content: String,
            images: ArrayList<ImageEntity>
        ) {
            val intent = Intent(context, CenterIntroActivity::class.java)
            intent.putExtra("title", title)
            intent.putExtra("content", content)
            intent.putExtra("images", images)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_center_intro

    override fun initView() {
        setOnClickListener(R.id.iv_back)
        initBanner()
    }

    override fun initData() {
        if (mTitle.isNullOrBlank()) {//表示不是预览
            val centerEntity = CenterRepository.getCenterIntroduction()
            binding.tvTitle.text = centerEntity?.title
            binding.tvCenterContent.setFirstLineIndent(centerEntity?.content.toString())
            if (centerEntity?.images.isNullOrEmpty()) {
                binding.banner.visibility = View.GONE
            } else {
                binding.banner.setDatas(centerEntity.images)
            }
        } else {
            binding.tvTitle.text = mTitle
            binding.tvCenterContent.setFirstLineIndent(mContent.toString())
            if (mImages.isEmpty()) {
                binding.banner.visibility = View.GONE
            } else {
                binding.banner.setDatas(mImages)
            }
        }
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.iv_back -> finish()
        }
    }

    /**
     * 初始化轮播图
     */
    private fun initBanner() {
        //轮播图
        binding.banner.setAdapter(TitbitBannerAdapter())
            .setIndicator(CircleIndicator(this))
            .addBannerLifecycleObserver(this)
            .setPageTransformer(DepthPageTransformer())
            .setOnBannerListener { data, position ->
                binding.banner.stop()
                val image = data as ImageEntity
                if (image.type == 2) {
                    previewVideo(image.path)
                } else {
                    previewPicture(image)
                }
            }.setIntercept(false)
    }

    /**
     * 预览图片
     */
    private fun previewPicture(image: ImageEntity) {
        if (image.path.isBlank()) {
            Toaster.show("图片加载失败")
            return
        }
        ImagePreviewActivity.start(this, listOf(image.path))
    }

    /**
     * 预览视频
     */
    private fun previewVideo(path: String) {
        if (path.isBlank()) {
            Toaster.show("视频加载失败")
            return
        }
        MovieDetailActivity.start(this, path)

    }

    override fun onResume() {
        super.onResume()
        binding.banner.start()
    }
}