package com.ygxj.self.ui.soul.music

import android.R.attr.visible
import android.R.id.message
import android.content.Context
import android.content.Intent
import android.view.View
import android.widget.SeekBar
import androidx.media3.exoplayer.ExoPlayer
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.google.common.collect.Multimaps.index
import com.hjq.toast.Toaster
import com.ygxj.self.R
import com.ygxj.self.data.entity.RelaxEntity
import com.ygxj.self.data.repository.RelaxRepository
import com.ygxj.self.databinding.ActivityMusicDetailBinding
import com.ygxj.self.databinding.ItemRelaxMusicBinding
import com.ygxj.self.ui.base.BaseActivity
import com.ygxj.self.utils.FileUtil
import com.ygxj.self.utils.TimeUtil
import com.ygxj.self.utils.gone
import com.ygxj.self.utils.loadImage
import com.ygxj.self.utils.visible
import kotlin.jvm.java
import kotlin.math.min

/**
 * 音乐详情
 */
class MusicDetailActivity : BaseActivity<ActivityMusicDetailBinding>() {

    private lateinit var player: ExoPlayer

    private var rotatingView: RotatingView? = null

    private var topRotatingImageView: TopRotatingImageView? = null

    private val mIndex by lazy { intent.getIntExtra("index", 0) }

    private val mMusicList by lazy { RelaxRepository.getRelaxMusicData() }

    companion object {
        fun start(context: Context, index: Int) {
            val intent = Intent(context, MusicDetailActivity::class.java)
            intent.putExtra("index", index)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_music_detail

    override fun initView() {
        setOnClickListener(
            R.id.ivBack,
            R.id.ivPre,
            R.id.ivPlay,
            R.id.ivNext,
            R.id.ivMode,
            R.id.ivList,
            R.id.rlCover,
            R.id.ivClose
        )
        initRecyclerView()
        rotatingView = RotatingView(binding.rlMusicCover)
        topRotatingImageView = TopRotatingImageView(binding.ivVinylPlay)

        MusicPlayerManager.initPlayer(this)
        MusicPlayerManager.setPlaybackListener(musicPlayBack)
        // 设置进度条监听
        binding.seekBar.setOnSeekBarChangeListener(seekBarChangeListener)
    }

    override fun initData() {
        binding.tvTitle.text = "播放列表（${mMusicList.size}）"
        MusicPlayerManager.setMusicList(mMusicList, mIndex)
        MusicPlayerManager.play()
    }

    /**
     * 初始化列表
     */
    private fun initRecyclerView() {
        binding.rv.setup {

            addType<RelaxEntity>(R.layout.item_relax_music)

            onBind {
                val bind = getBinding<ItemRelaxMusicBinding>()
                bind.tvMusic.text = getModel<RelaxEntity>().name
                bind.tvDuration.text = FileUtil.getLocalVideoDuration(getModel<RelaxEntity>().path)
            }

            onClick(R.id.item) {
                MusicPlayerManager.playAtIndex(layoutPosition)
            }
        }
        mMusicList[mIndex].checked = true
        binding.rv.models = mMusicList

    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivBack -> finish()

            R.id.ivPre -> MusicPlayerManager.playPrevious()

            R.id.ivPlay -> MusicPlayerManager.togglePlayPause()

            R.id.ivNext -> MusicPlayerManager.playNext()

            R.id.ivMode -> {
                val newMode = MusicPlayerManager.switchPlayMode()
                showPlayModeToast(newMode)
            }

            R.id.ivList -> binding.rlCover.visible()

            R.id.ivClose, R.id.rlCover -> binding.rlCover.gone()
        }
    }


    /**
     * 进度条回调
     */
    private var seekBarChangeListener = object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser) {
                val duration = MusicPlayerManager.getDuration()
                val position = (progress / 100.0 * duration).toLong()
                MusicPlayerManager.seekTo(position)
            }
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    }

    /**
     * 音乐播放回调
     */
    private var musicPlayBack = object : MusicPlayerManager.PlaybackListener {
        override fun onPlayStateChanged(isPlaying: Boolean) {
            binding.ivPlay.isSelected = isPlaying
            if (isPlaying) {
                rotatingView?.start()
                topRotatingImageView?.reset()
            } else {
                rotatingView?.pause()
                topRotatingImageView?.rotateTo(-25f)
            }
        }

        override fun onMusicChanged(music: RelaxEntity, index: Int) {
            // 更新音乐信息显示
            binding.tvMusicTitle.text = music.name
            binding.ivMusicPicture.loadImage(music.image)
            mMusicList.forEach { it.checked = false }
            mMusicList[index].checked = true
            binding.rv.bindingAdapter.notifyDataSetChanged()
            binding.rv.scrollToPosition(index)
        }

        override fun onProgressChanged(position: Long, duration: Long) {
            updateProgress(position, duration)
        }

        override fun onPlayModeChanged(playMode: MusicPlayerManager.PlayMode) {
            updatePlayModeUI(playMode)
        }
    }

    /**
     * 更新进度条
     */
    private fun updateProgress(position: Long, duration: Long) {
        if (duration > 0) {
            val progress = (position * 100 / duration).toInt()
            binding.seekBar.progress = progress

            // 更新时间显示
            binding.tvProgress.text = TimeUtil.formatMillionSeconds(position)
            binding.tvDuration.text = TimeUtil.formatMillionSeconds(duration)
        }
    }

    /**
     * 更新播放模式UI
     */
    private fun updatePlayModeUI(playMode: MusicPlayerManager.PlayMode) {
        when (playMode) {
            MusicPlayerManager.PlayMode.SEQUENCE -> {
                binding.ivMode.isSelected = false
            }

            MusicPlayerManager.PlayMode.RANDOM -> {
                binding.ivMode.isSelected = true
            }

            else -> {

            }
        }
    }

    private fun showPlayModeToast(playMode: MusicPlayerManager.PlayMode) {
     when (playMode) {
            MusicPlayerManager.PlayMode.SEQUENCE -> {
                Toaster.show("顺序播放")
                binding.ivMode.setBackgroundResource(R.drawable.ic_music_order_play)
            }
            MusicPlayerManager.PlayMode.RANDOM -> {
                Toaster.show("随机播放")
                binding.ivMode.setBackgroundResource(R.drawable.ic_music_shuffle_play)
            }
            MusicPlayerManager.PlayMode.REPEAT_ONE -> {
                Toaster.show("单曲循环")
                binding.ivMode.setBackgroundResource(R.drawable.ic_music_loop_play)
            }
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        MusicPlayerManager.release()
        rotatingView?.cancel()
    }

}