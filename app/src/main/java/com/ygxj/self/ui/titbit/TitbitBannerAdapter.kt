package com.ygxj.self.ui.titbit

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ygxj.self.data.entity.ImageEntity
import com.ygxj.self.databinding.ItemBannerBinding
import com.ygxj.self.utils.loadImage
import com.ygxj.self.utils.show
import com.youth.banner.adapter.BannerAdapter

class TitbitBannerAdapter(data: MutableList<ImageEntity>? = null) :
    BannerAdapter<ImageEntity, TitbitBannerAdapter.BannerViewHolder>(data) {

    override fun onCreateHolder(
        parent: ViewGroup,
        viewType: Int,
    ): BannerViewHolder {
        val binding = ItemBannerBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BannerViewHolder(binding)
    }

    override fun onBindView(
        holder: <PERSON><PERSON>iewHolder,
        data: ImageEntity,
        position: Int,
        size: Int,
    ) {
        holder.bind(data)
    }

    inner class BannerViewHolder(private val binding: ItemBannerBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(imageEntity: ImageEntity) {
            binding.apply {
                // 设置图片
                ivBanner.loadImage(imageEntity.path)
                // 根据类型显示或隐藏视频相关控件
                ivPlay.show(imageEntity.type == 2)
                vMask.show(imageEntity.type == 2)
            }
        }
    }
}