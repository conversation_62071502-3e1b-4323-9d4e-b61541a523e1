package com.ygxj.self.ui.psychology


import android.content.Context
import android.content.Intent
import android.view.View
import androidx.databinding.DataBindingUtil.getBinding
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.drake.brv.utils.models
import com.drake.brv.utils.mutable
import com.drake.brv.utils.setup
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.google.common.collect.Multimaps.index
import com.hjq.toast.Toaster
import com.ygxj.self.R
import com.ygxj.self.data.entity.PsychologyEntity
import com.ygxj.self.data.repository.KnowRepository
import com.ygxj.self.databinding.ActivityCopyBinding
import com.ygxj.self.databinding.ActivityPsychologyBinding
import com.ygxj.self.databinding.ActivityPsychologyDetailBinding
import com.ygxj.self.databinding.ItemPsychologyBinding
import com.ygxj.self.ui.base.BaseActivity
import com.ygxj.self.utils.gone
import com.ygxj.self.utils.show
import kotlin.jvm.java

/**
 * 你不知道的心理学详情
 */
class PsychologyDetailActivity : BaseActivity<ActivityPsychologyDetailBinding>() {

    private val mPsychologyData by lazy { intent.getSerializableExtra("psychologyData") as ArrayList<PsychologyEntity> }

    private val mBookIndex by lazy { intent.getIntExtra("bookIndex", 0) }

    companion object {
        fun start(context: Context, psychologyData: ArrayList<PsychologyEntity>, index: Int) {
            val intent = Intent(context, PsychologyDetailActivity::class.java)
            intent.putExtra("psychologyData", psychologyData)
            intent.putExtra("bookIndex", index)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_psychology_detail

    override fun initView() {
        setOnClickListener(R.id.btnPreBook, R.id.btnNextBook, R.id.ivBack)
    }

    override fun initData() {
        binding.tvTitle.text = mPsychologyData.getOrNull(mBookIndex)?.title
        binding.pageFlipView.setShowContent(this, mPsychologyData.getOrNull(mBookIndex)?.content)

        binding.btnPreBook.show(mBookIndex != 0)
        binding.btnNextBook.show(mBookIndex < mPsychologyData.size - 1)
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivBack -> finish()

            R.id.btnPreBook -> {
                if (mBookIndex == 0) {
                    Toaster.show("已经是第一本啦")
                    return
                }
                start(this@PsychologyDetailActivity, mPsychologyData, mBookIndex - 1)
                finish()
            }

            R.id.btnNextBook -> {
                if (mBookIndex == mPsychologyData.size - 1) {
                    Toaster.show("已经是最后一本啦")
                    return
                }
                start(this@PsychologyDetailActivity, mPsychologyData, mBookIndex + 1)
                finish()
            }
        }
    }
}