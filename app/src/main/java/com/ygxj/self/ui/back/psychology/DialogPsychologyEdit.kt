package com.ygxj.self.ui.back.psychology

import android.annotation.SuppressLint
import android.content.Context
import androidx.databinding.DataBindingUtil
import com.hjq.toast.Toaster
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.qmuiteam.qmui.kotlin.onClick
import com.qmuiteam.qmui.widget.dialog.QMUIDialog
import com.ygxj.self.R
import com.ygxj.self.data.entity.PsychologyEntity
import com.ygxj.self.data.repository.KnowRepository
import com.ygxj.self.databinding.DialogPsychologyEditBinding
import com.ygxj.self.utils.DefaultData
import com.ygxj.self.utils.DialogUtil
import com.ygxj.self.utils.GlideEngine
import com.ygxj.self.utils.ImageSandboxFileEngine
import com.ygxj.self.utils.loadImage

/**
 * 你所不知道的心理学编辑
 */
@SuppressLint("ViewConstructor")
class DialogPsychologyEdit(
    context: Context,
    var psychology: PsychologyEntity? = null,
    var onFinish: () -> Unit
) : CenterPopupView(context) {

    private var imagePath: String = ""

    private lateinit var binding: DialogPsychologyEditBinding

    override fun getImplLayoutId() = R.layout.dialog_psychology_edit

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate() {
        super.onCreate()
        binding = DataBindingUtil.bind(popupImplView)!!

        if (psychology == null) {
            binding.tvTitle.text = "添加"
        } else {
            binding.tvTitle.text = "编辑"
            binding.etName.setText(psychology?.title)
            binding.tvType.text = psychology?.type
            binding.ivTemp.loadImage(psychology?.image)
            imagePath = psychology?.image.toString()
            binding.etContent.setText(psychology?.content)
        }

        binding.btnConfirm.onClick {
            save()
        }
        binding.btnCancel.onClick {
            dismiss()
        }

        binding.btnSelectImage.onClick {
            selectImage()
        }

        binding.tvType.onClick {
            selectType()
        }
    }

    /**
     * 选择类别
     */
    private fun selectType(){
        val s = DefaultData.psychologyType
        QMUIDialog.CheckableDialogBuilder(context).apply {
            setTitle("请选择类别")
            addItems(s) { dialog, which ->
                dialog.dismiss()
                binding.tvType.text = s[which]
            }
            create().show()
        }
    }

    /**
     * 选择图片
     */
    private fun selectImage() {
        PictureSelector.create(context)
            .openGallery(SelectMimeType.ofImage())
            .setMaxSelectNum(1)
            .setImageEngine(GlideEngine.createGlideEngine())
            .setSandboxFileEngine(ImageSandboxFileEngine())
            .isDisplayCamera(false)
            .forResult(object : OnResultCallbackListener<LocalMedia?> {
                override fun onResult(result: ArrayList<LocalMedia?>?) {
                    imagePath = result?.first()?.availablePath.toString()
                    binding.ivTemp.loadImage(imagePath)
                }
                override fun onCancel() {}
            })
    }

    /**
     * 保存
     */
    private fun save() {
        if (binding.etName.text.isNullOrBlank()) {
            Toaster.show("请输入标题")
            return
        }
        if (binding.tvType.text.isNullOrBlank()) {
            Toaster.show("请选择类别")
            return
        }
        if (imagePath.isBlank()) {
            Toaster.show("请选择图片")
            return
        }
        if (binding.etContent.text.isNullOrBlank()) {
            Toaster.show("请输入内容")
            return
        }
        var tip = ""
        if (psychology == null) {
            psychology = PsychologyEntity()
            tip = "添加成功"
        } else {
            tip = "修改成功"
        }

        //再保存中心活动花絮
        psychology?.title = binding.etName.text.toString()
        psychology?.content = binding.etContent.text.toString()
        psychology?.type = binding.tvType.text.toString()
        psychology?.image = imagePath
        psychology?.time = System.currentTimeMillis()
        //保存中心介绍
        KnowRepository.savePsychology(psychology!!)

        DialogUtil.showAutoDismissSuccessDialog(context, tip)
        onFinish()
        dismiss()
    }

    fun build(): BasePopupView {
        return XPopup.Builder(context)
            .isViewMode(true)
            .moveUpToKeyboard(false)
            .asCustom(this)
    }
}