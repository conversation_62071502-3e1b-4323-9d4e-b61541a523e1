package com.ygxj.self.ui.config


import android.content.Context
import android.content.Intent
import com.hjq.toast.Toaster
import com.qmuiteam.qmui.kotlin.onClick
import com.ygxj.self.R
import com.ygxj.self.databinding.ActivityConfigBinding
import com.ygxj.self.ui.base.BaseActivity
import com.ygxj.self.ui.main.MainActivity
import com.ygxj.self.utils.DialogUtil

/**
 * 第一次进入app后需要配置一些基本信息
 */
class ConfigActivity : BaseActivity<ActivityConfigBinding>() {

    //是否是从后台进入的
    private val mBackground by lazy { intent.getBooleanExtra("background", false) }

    companion object {
        fun start(context: Context, background: Boolean = false) {
            val intent = Intent(context, ConfigActivity::class.java)
            intent.putExtra("background", background)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId() = R.layout.activity_config

    override fun initView() {

    }

    override fun initData() {
        binding.tvTitle.setText(ConfigManager.title)
        binding.tvSubTitle.setText(ConfigManager.subTitle)

        binding.rgLogo.setOnCheckedChangeListener { _, i ->
            when (i) {
                R.id.rbShow -> ConfigManager.showLogo = true
                R.id.rbNotShow -> ConfigManager.showLogo = false
            }
        }

        binding.rgIndustry.setOnCheckedChangeListener { _, i ->
            when (i) {
                R.id.rbSchool1 -> ConfigManager.industry = 1
                R.id.rbSchool2 -> ConfigManager.industry = 2
                R.id.rbSchool3 -> ConfigManager.industry = 3
            }
        }

        if (ConfigManager.showLogo) {
            binding.rbShow.isChecked = true
        } else {
            binding.rbNotShow.isChecked = true
        }

        when (ConfigManager.industry) {
            1 -> binding.rbSchool1.isChecked = true
            2 -> binding.rbSchool2.isChecked = true
            3 -> binding.rbSchool3.isChecked = true
        }

        binding.btnSave.onClick {
            saveConfig()
        }
    }

    /**
     * 保存配置信息
     */
    private fun saveConfig() {
        if (binding.tvTitle.text.isNullOrBlank()) {
            Toaster.show("请输入主标题")
            return
        }

        DialogUtil.showDialogWithConfirmCallBackAndCancelButton(
            this@ConfigActivity,
            "提示",
            "确认保存吗？"
        ) { dialog, _ ->
            dialog.dismiss()
            ConfigManager.title = binding.tvTitle.text.toString()
            ConfigManager.subTitle = binding.tvSubTitle.text.toString()
            if (!mBackground) {
                startActivity(MainActivity::class.java)
            }
            finish()
        }

    }

}