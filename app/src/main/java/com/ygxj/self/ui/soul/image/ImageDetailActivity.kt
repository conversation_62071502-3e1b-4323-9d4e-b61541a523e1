package com.ygxj.self.ui.soul.image

import android.content.Context
import android.content.Intent
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.util.Log
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.ygxj.self.R
import com.ygxj.self.data.repository.RelaxRepository
import com.ygxj.self.databinding.ActivityImagePreviewBinding
import com.ygxj.self.ui.base.BaseActivity
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.Locale

/**
 * 图片预览
 */
class ImageDetailActivity : BaseActivity<ActivityImagePreviewBinding>(), TextToSpeech.OnInitListener {

    private lateinit var tts: TextToSpeech

    private val dataList by lazy { RelaxRepository.getRelaxPictureData() }

    private var mIndex = 0

    private var job: Job? = null

    private var jobTts: Job? = null

    companion object {
        fun start(context: Context, index: Int = 0) {
            val intent = Intent(context, ImageDetailActivity::class.java)
            intent.putExtra("index", index)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId() = R.layout.activity_image_preview

    override fun initView() {
        initTTS()

    }

    override fun initData() {
        intent.getIntExtra("index", 0)
        initViewPager2()
    }

    private fun initViewPager2() {
        binding.viewPager2.apply {
            adapter = ImageDetailAdapter(dataList.map { it.image }) {
                finish()
            }
            orientation = ViewPager2.ORIENTATION_HORIZONTAL

            registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    job?.cancel()
                    jobTts?.cancel()
                    mIndex = position
                    //判断是否有简介，有就播报简介，没有就过10秒自动跳转下一张图
                    if (dataList[mIndex].info.isBlank()) {
                        //10秒后自动跳转到下一张图
                        job = lifecycleScope.launch {
                            delay(10000)
                            if (mIndex == dataList.size - 1) {
                                mIndex = 0
                                setCurrentItem(mIndex, false)
                            } else {
                                mIndex += 1
                               currentItem = mIndex
                            }
                        }
                    } else {
                        //延时1秒开始播报tts
                        jobTts = lifecycleScope.launch {
                            delay(1000)
                            speak(dataList[mIndex].info)
                        }
                    }
                }
            })

            setCurrentItem(mIndex, false)
        }
    }

    /**
     * 初始化tts
     */
    private fun initTTS() {
        tts = TextToSpeech(this, this)
        tts.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
            override fun onStart(utteranceId: String) {
                // 播报开始
                runOnUiThread {
                    resetAutoExitTimer()
                }
            }

            override fun onDone(utteranceId: String) {
                Timber.e("播报完成")
                // 播报完成
                if (mIndex == dataList.size - 1) {
                    mIndex = 0
                    runOnUiThread {
                        binding.viewPager2.setCurrentItem(mIndex, false)
                    }
                } else {
                    mIndex += 1
                    runOnUiThread {
                        binding.viewPager2.currentItem = mIndex
                    }
                }
            }

            override fun onError(utteranceId: String) {
                // 播报错误
            }
        })
    }

    /**
     * 初始化tts
     */
    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            // 设置语言为中文
            val result = tts.setLanguage(Locale.CHINESE)
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Log.e("TTS", "中文语言不支持")
            } else {
                Log.e("TTS", "初始化成功")
            }
        } else {
            Log.e("TTS", "TextToSpeech初始化失败")
        }
    }

    /**
     * tts播报
     */
    private fun speak(text: String) {
        tts.speak(text, TextToSpeech.QUEUE_FLUSH, null, "")
    }


    override fun onDestroy() {
        // 释放TextToSpeech资源
        if (::tts.isInitialized) {
            tts.stop()
            tts.shutdown()
        }
        job?.cancel()
        jobTts?.cancel()
        super.onDestroy()

    }
}