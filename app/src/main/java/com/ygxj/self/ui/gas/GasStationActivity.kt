package com.ygxj.self.ui.gas


import com.ygxj.self.R
import com.ygxj.self.databinding.ActivityCopyBinding
import com.ygxj.self.databinding.ActivityGasStationBinding
import com.ygxj.self.ui.base.BaseActivity

/**
 * 心理加油站
 */
class GasStationActivity : BaseActivity<ActivityGasStationBinding>() {

    override fun getLayoutId(): Int = R.layout.activity_gas_station

    override fun initView() {

    }

    override fun initData() {

    }

}