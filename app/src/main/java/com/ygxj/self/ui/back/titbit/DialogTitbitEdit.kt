package com.ygxj.self.ui.back.titbit

import android.annotation.SuppressLint
import android.content.Context
import android.view.MotionEvent
import android.widget.EditText
import androidx.core.content.ContentProviderCompat.requireContext
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.FileUtils
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.models
import com.drake.brv.utils.mutable
import com.drake.brv.utils.setup
import com.hjq.toast.Toaster
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.qmuiteam.qmui.kotlin.onClick
import com.ygxj.self.R
import com.ygxj.self.data.entity.Footer
import com.ygxj.self.data.entity.ImageEntity
import com.ygxj.self.data.entity.TitbitEntity
import com.ygxj.self.data.repository.CenterRepository
import com.ygxj.self.data.repository.ImageRepository
import com.ygxj.self.data.repository.TitbitRepository
import com.ygxj.self.databinding.DialogTitbitEditBinding
import com.ygxj.self.databinding.ItemCenterImageBinding
import com.ygxj.self.ui.image.ImagePreviewActivity
import com.ygxj.self.ui.soul.movie.MovieDetailActivity
import com.ygxj.self.utils.DialogUtil
import com.ygxj.self.utils.GlideEngine
import com.ygxj.self.utils.ImageSandboxFileEngine
import com.ygxj.self.utils.loadImage
import com.ygxj.self.utils.show
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.collections.forEach

/**
 * 中心活动花絮编辑
 */
@SuppressLint("ViewConstructor")
class DialogTitbitEdit(
    context: Context,
    var titbit: TitbitEntity? = null,
    var onFinish: () -> Unit
) : CenterPopupView(context) {

    private lateinit var binding: DialogTitbitEditBinding

    override fun getImplLayoutId() = R.layout.dialog_titbit_edit

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate() {
        super.onCreate()
        binding = DataBindingUtil.bind(popupImplView)!!
        initRecyclerView()

        if (titbit == null) {
            binding.tvTitle.text = "添加"
            binding.rv.models = emptyList<ImageEntity>()
        } else {
            binding.tvTitle.text = "编辑"
            binding.etName.setText(titbit?.title)
            binding.etContent.setText(titbit?.content)
            binding.rv.models = titbit?.images

        }
        binding.rv.bindingAdapter.addFooter(Footer())

        binding.btnConfirm.onClick {
            save()
        }
        binding.btnCancel.onClick {
            dismiss()
        }

        //解决edittext和scrollview 的滑动冲突问题
        binding.etContent.setOnTouchListener { view, motionEvent ->
            if ((view.id == R.id.etContent && canVerticalScroll(binding.etContent))) {
                view.parent.requestDisallowInterceptTouchEvent(true)
                // 否则将事件交由其父类处理
                if (motionEvent.action == MotionEvent.ACTION_UP) {
                    view.parent.requestDisallowInterceptTouchEvent(false)
                }
            }
            false
        }
    }

    // 判断当前EditText是否可滚动
    private fun canVerticalScroll(editText: EditText): Boolean {
        return editText.lineCount > editText.maxLines
    }

    /**
     * 初始化列表
     */
    private fun initRecyclerView() {
        binding.rv.setup {
            addType<ImageEntity>(R.layout.item_center_image)

            addType<Footer>(R.layout.item_center_footer)

            onBind {
                if (itemViewType == R.layout.item_center_image) {
                    val bind = getBinding<ItemCenterImageBinding>()
                    val model = getModel<ImageEntity>()
                    bind.ivCover.loadImage(model.path)
                    bind.llMask.show(model.type == 2)
                }
            }

            //预览图片或视频
            R.id.item.onClick {
                val model = getModel<ImageEntity>()
                if (model.type == 1) {
                    ImagePreviewActivity.start(context, listOf(model.path))
                } else {
                    MovieDetailActivity.start(context, model.path)
                }
            }
            //删除
            R.id.iv_delete.onClick {
                val model = getModel<ImageEntity>()
                FileUtils.delete(model.path)
                ImageRepository.deleteImage(model)
                binding.rv.mutable.removeAt(layoutPosition)
                binding.rv.bindingAdapter.notifyItemRemoved(layoutPosition)
            }
            //添加图片
            R.id.btn_add_image.onClick {
                selectImage()
            }
            //添加视频
            R.id.btn_add_video.onClick {
                selectVideo()
            }
        }
    }

    /**
     * 选择图片
     */
    private fun selectImage() {
        PictureSelector.create(context)
            .openGallery(SelectMimeType.ofImage())
            .setImageEngine(GlideEngine.createGlideEngine())
            .setSandboxFileEngine(ImageSandboxFileEngine())
            .isDisplayCamera(false)
            .forResult(object : OnResultCallbackListener<LocalMedia?> {
                override fun onResult(result: ArrayList<LocalMedia?>?) {
                    result?.forEach { localMedia ->
                        val imageEntity = ImageEntity(1, localMedia?.availablePath.toString())
                        binding.rv.bindingAdapter.addModels(listOf(imageEntity))
                    }
                    scrollToBottom()
                }

                override fun onCancel() {}
            })
    }

    /**
     * 选择视频
     */
    private fun selectVideo() {
        PictureSelector.create(context)
            .openGallery(SelectMimeType.ofVideo())
            .setImageEngine(GlideEngine.createGlideEngine())
            .setSandboxFileEngine(ImageSandboxFileEngine())
            .isDisplayCamera(false)
            .forResult(object : OnResultCallbackListener<LocalMedia?> {
                override fun onResult(result: ArrayList<LocalMedia?>?) {
                    result?.forEach { localMedia ->
                        val imageEntity = ImageEntity(2, localMedia?.availablePath.toString())
                        binding.rv.bindingAdapter.addModels(listOf(imageEntity))
                    }
                    scrollToBottom()
                }

                override fun onCancel() {}
            })
    }

    /**
     * 滚动到底部
     */
    private fun scrollToBottom() {
        lifecycleScope.launch {
            delay(300)
            binding.scrollview.post {
                val child = binding.scrollview.getChildAt(binding.scrollview.childCount - 1)
                val bottom = child.bottom + binding.scrollview.paddingBottom
                binding.scrollview.scrollTo(0, bottom)
            }
        }
    }

    /**
     * 保存
     */
    private fun save() {
        if (binding.etName.text.isNullOrBlank()) {
            Toaster.show("请输入名称")
            return
        }
        if (binding.etContent.text.isNullOrBlank()) {
            Toaster.show("请输入内容")
            return
        }
        var tip = ""
        if (titbit == null) {
            titbit = TitbitEntity()
            tip = "添加成功"
        } else {
            tip = "修改成功"
        }
        titbit?.images?.forEach {
            it.delete()
        }
        val imageList = binding.rv.mutable as ArrayList<ImageEntity>
        //先保存图片
        imageList.forEach { ImageRepository.saveImage(it) }
        //再保存中心活动花絮
        titbit?.title = binding.etName.text.toString()
        titbit?.content = binding.etContent.text.toString()
        titbit?.images = imageList
        titbit?.time = System.currentTimeMillis()
        val total = TitbitRepository.getTitbitCount()
        titbit?.sort = total + 1
        //保存中心介绍
        TitbitRepository.saveTitbit(titbit)

        DialogUtil.showAutoDismissSuccessDialog(context, tip)
        onFinish()
        dismiss()
    }

    fun build(): BasePopupView {
        return XPopup.Builder(context)
            .isViewMode(true)
            .moveUpToKeyboard(false)
            .asCustom(this)
    }
}