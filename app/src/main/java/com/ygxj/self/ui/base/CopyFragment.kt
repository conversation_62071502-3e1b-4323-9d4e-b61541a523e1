package com.ygxj.self.ui.base


import com.ygxj.self.R
import com.ygxj.self.databinding.FragmentCopyBinding
import com.ygxj.self.ui.main.MainActivity

/**
 * 可进行拷贝的副本
 */
class CopyFragment : BaseFragment<MainActivity, FragmentCopyBinding>() {

    companion object {
        fun newInstance(): CopyFragment {
            return CopyFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_copy
    }

    override fun initView() {}

    override fun initData() {}
}