package com.ygxj.self.ui.soul.movie

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.core.net.toUri
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.ygxj.self.R
import com.ygxj.self.databinding.ActivityMovieDetailBinding
import com.ygxj.self.ui.base.BaseActivity
import timber.log.Timber

/**
 * 电影详情
 */
class MovieDetailActivity : BaseActivity<ActivityMovieDetailBinding>() {

    private lateinit var player: ExoPlayer

    private val mPath by lazy { intent.getStringExtra("path")}

    private val mTitle by lazy { intent.getStringExtra("title") }

    companion object {
        fun start(context: Context, path: String, title: String = "") {
            val intent = Intent(context, MovieDetailActivity::class.java)
            intent.putExtra("path", path)
            intent.putExtra("title", title)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_movie_detail

    override fun initView() {
        setOnClickListener(R.id.iv_back)
        initVideoPlay()
    }

    override fun initData() {
        binding.tvTitle.text = mTitle
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.iv_back -> finish()
        }
    }

    // 隐藏状态栏和导航栏
    override fun createStatusBarConfig(): ImmersionBar {
        return super.createStatusBarConfig()
            .hideBar(BarHide.FLAG_HIDE_BAR)
    }

    /**
     * 初始化视频播放器
     */
    private fun initVideoPlay() {
        // 初始化 ExoPlayer
        player = ExoPlayer.Builder(this).build()
        binding.playerView.player = player
        // 使用新的方法设置监听器
        binding.playerView.setControllerVisibilityListener(controllerVisibilityListener)

        val mediaItem = MediaItem.fromUri("${mPath}".toUri())

        player.repeatMode = Player.REPEAT_MODE_ONE
        // 设置媒体源
        player.setMediaItem(mediaItem)
        // 设置监听器
        player.addListener(playerListener)
        // 准备播放器
        player.prepare()

        player.playWhenReady = true
    }

    // 使用新的 ControllerVisibilityListener 接口
    private val controllerVisibilityListener =
        PlayerView.ControllerVisibilityListener { visibility ->
            when (visibility) {
                View.VISIBLE -> {
                    Timber.Forest.e("控制层显示")
                    // 控制层显示时的操作
                    binding.rlTitle.visibility = View.VISIBLE
                }

                View.GONE -> {
                    Timber.Forest.e("控制层隐藏")
                    // 控制层隐藏时的操作
                    binding.rlTitle.visibility = View.GONE
                }
            }
        }

    /**
     * 播放状态监听
     */
    private val playerListener = object : Player.Listener {
        override fun onIsPlayingChanged(isPlaying: Boolean) {
            super.onIsPlayingChanged(isPlaying)
            Timber.Forest.e("播放状态改变: $isPlaying")
        }

        override fun onPlaybackStateChanged(playbackState: Int) {
            super.onPlaybackStateChanged(playbackState)
            when (playbackState) {
                Player.STATE_IDLE -> {
                    Timber.Forest.e("空闲中")
                }

                Player.STATE_BUFFERING -> {
                    Timber.Forest.e("缓冲中")
                }

                Player.STATE_READY -> {
                    Timber.Forest.e("准备完成")
                    binding.progressBar.visibility = View.GONE
                }

                Player.STATE_ENDED -> {
                    Timber.Forest.e("播放完成")

                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        player.release()
    }

}