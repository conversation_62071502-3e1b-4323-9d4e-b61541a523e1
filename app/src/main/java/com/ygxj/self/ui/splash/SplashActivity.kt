package com.ygxj.self.ui.splash


import android.annotation.SuppressLint
import androidx.lifecycle.lifecycleScope
import com.drake.net.time.Interval
import com.ygxj.self.AppApplication
import com.ygxj.self.R
import com.ygxj.self.data.repository.CenterRepository
import com.ygxj.self.data.repository.ExploreRepository
import com.ygxj.self.data.repository.FunnyRepository
import com.ygxj.self.data.repository.GasStationRepository
import com.ygxj.self.data.repository.ImageRepository
import com.ygxj.self.data.repository.KnowRepository
import com.ygxj.self.data.repository.RelaxRepository
import com.ygxj.self.data.repository.ScaleRepository
import com.ygxj.self.data.repository.ServiceRoomRepository
import com.ygxj.self.data.repository.StyleRepository
import com.ygxj.self.data.repository.TitbitRepository
import com.ygxj.self.data.repository.TrainRepository
import com.ygxj.self.data.repository.UserRepository
import com.ygxj.self.databinding.ActivitySplashBinding
import com.ygxj.self.ui.config.ConfigManager
import com.ygxj.self.ui.base.BaseActivity
import com.ygxj.self.ui.main.MainActivity
import com.ygxj.self.utils.AssetsUnzipUtils
import com.ygxj.self.utils.DefaultData
import com.ygxj.self.utils.DialogUtil
import com.ygxj.self.utils.gone
import com.ygxj.self.utils.visible
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.TimeUnit


/**
 *  闪屏界面
 */
@SuppressLint("CustomSplashScreen")
class SplashActivity : BaseActivity<ActivitySplashBinding>() {

    //每秒执行一次的计时器
    private val interval: Interval by lazy { Interval(1, TimeUnit.SECONDS) }

    override fun getLayoutId(): Int = R.layout.activity_splash

    override fun initView() {

    }

    override fun initData() {
        binding.tvTitle.text = ConfigManager.title
        //默认数据为空，开始解压数据
        if (UserRepository.getAllUser().isEmpty()) {
            binding.llLogo.gone()
            binding.llProgress.visible()
            startUnzip()
        } else {
            //有默认数据，跳转首页
            binding.llLogo.visible()
            binding.llProgress.gone()
            lifecycleScope.launch {
                delay(1000)
                startActivity(MainActivity::class.java)
                finish()
            }
        }
    }

    /**
     * 添加默认数据
     */
    fun addDefaultData() {
        //添加管理员
        UserRepository.saveUserData(DefaultData.admin)
        UserRepository.saveUserData(DefaultData.superAdmin)
        //添加中心介绍内容
        CenterRepository.addDefaultData(this)
        //添加中心活动花絮
        TitbitRepository.addDefaultData()
        //添加心理服务室
        ServiceRoomRepository.addDefaultData()
        //添加趣味测试
        FunnyRepository.addDefaultFunnyData()
        FunnyRepository.addDefaultTopicData()
        //添加量表
        ScaleRepository.addDefaultScaleData()
        ScaleRepository.addDefaultScaleTopicData()
        //添加心理咨询师
        StyleRepository.savePsychologists(DefaultData.psyList)
        //添加你所不知道的心理学
        KnowRepository.savePsychologies(DefaultData.psychologyList)
        //添加放松图片
        RelaxRepository.saveRelaxList(DefaultData.pictureList)
        //添加放松音乐
        RelaxRepository.saveRelaxList(DefaultData.musicList)
        //添加放松电影
        RelaxRepository.saveRelaxList(DefaultData.movieList)
        //添加心理加油站
        GasStationRepository.saveGasStations(DefaultData.stationList)
        //添加心理探索
        ExploreRepository.saveExplore(DefaultData.exploreList)
        //添加行为训练
        TrainRepository.saveTrain(DefaultData.trainList)
    }

    /**
     * 开始解压
     */
    private fun startUnzip() {
        updateProgress()
        lifecycleScope.launch(Dispatchers.IO) {
            //解压assets下的self.zip文件到/data/data/packageName/files/self目录下
            val success = AssetsUnzipUtils.unzipAssetsFile(this@SplashActivity, "self.zip")
            withContext(Dispatchers.Main) {
                if (success) {
                    unzipSuccess()
                } else {
                    unzipFailed()
                }
            }
        }
    }


    /**
     * 解压成功
     */
    private fun unzipSuccess() {
        binding.progressBar.progress = 100
        addDefaultData()
        startActivity(MainActivity::class.java)
        finish()
    }

    /**
     * 解压失败
     */
    private fun unzipFailed() {
        DialogUtil.showAutoDismissSuccessDialogWithListener(
            this@SplashActivity,
            "资源解压失败，请检查设备是否有足够的空间，并确保程序安装包完整"
        ) {
            finish()
        }
    }

    /**
     * 更新进度条
     */
    private fun updateProgress() {
        interval.subscribe {
            if (it.toInt() < 98) {
                binding.progressBar.progress = it.toInt()
            } else {
                binding.progressBar.progress = 98
            }
        }.start()
    }


    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        //super.onBackPressed()
    }

    override fun onDestroy() {
        super.onDestroy()
        interval.cancel()
    }
}