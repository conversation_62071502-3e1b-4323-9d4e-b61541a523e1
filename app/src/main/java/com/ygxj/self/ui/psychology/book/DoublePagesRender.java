/*
 * Copyright (C) 2016 eschao <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ygxj.self.ui.psychology.book;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Message;

import com.eschao.android.widget.pageflip.Page;
import com.eschao.android.widget.pageflip.PageFlip;
import com.eschao.android.widget.pageflip.PageFlipState;
import com.ygxj.self.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Double pages render
 * <p>
 * Some key points here:
 * <ul>
 *     <li>First page is which page user is clicking on or moving by finger
 *          Sometimes it is left page on screen, sometimes it is right page.
 *          Second page is leftover page against the first page
 *     </li>
 *     <li>mPageNo is always the number of left page instead of first page</li>
 * </ul>
 * </p>
 * <p>
 * Every screen 'Page' contains 3 page contents, so it need 3 textures:
 * <ul>
 *     <li>First texture: first page content of this 'Page'</li>
 *     <li>Back texture: the second page content of this 'Page'</li>
 *     <li>Second texture: the third page content of this 'Page'</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 */

public class DoublePagesRender extends PageRender {

    private List<List<String>> mShowContentList = new ArrayList<>();
    private String mShowContent;

    /**
     * Constructor
     */
    public DoublePagesRender(Context context, PageFlip pageFlip,
                             Handler handler, int pageNo, String showContent) {
        super(context, pageFlip, handler, pageNo);
        this.mShowContent = showContent;
    }

    /**
     * Draw page frame
     */
    public void onDrawFrame() {
        // 1. delete unused textures to save memory
        mPageFlip.deleteUnusedTextures();

        // 2. there are two pages for representing the whole screen, we need to
        // draw them one by one
        final Page first = mPageFlip.getFirstPage();
        final Page second = mPageFlip.getSecondPage();

        // 3. check if the first texture is valid for first page, if not,
        // create it with relative content
        if (!first.isFirstTextureSet()) {
            drawPage(first.isLeftPage() ? mPageNo : mPageNo + 1);
            first.setFirstTexture(mBitmap);
        }

        // 4. check if the first texture is valid for second page
        if (!second.isFirstTextureSet()) {
            drawPage(second.isLeftPage() ? mPageNo : mPageNo + 1);
            second.setFirstTexture(mBitmap);
        }

        // 5. handle drawing command triggered from finger moving and animating
        if (mDrawCommand == DRAW_MOVING_FRAME ||
                mDrawCommand == DRAW_ANIMATING_FRAME) {
            // before drawing, check if back texture of first page is valid
            // Remember: the first page is always the fold page
            if (!first.isBackTextureSet()) {
                drawPage(first.isLeftPage() ? mPageNo - 1 : mPageNo + 2);
                first.setBackTexture(mBitmap);
            }

            // check the second texture of first page is valid.
            if (!first.isSecondTextureSet()) {
                drawPage(first.isLeftPage() ? mPageNo - 2 : mPageNo + 3);
                first.setSecondTexture(mBitmap);
            }

            // draw frame for page flip
            mPageFlip.drawFlipFrame();
        }
        // draw stationary page without flipping
        else if (mDrawCommand == DRAW_FULL_PAGE) {
            mPageFlip.drawPageFrame();
        }

        // 6. send message to main thread to notify drawing is ended so that
        // we can continue to calculate next animation frame if need.
        // Remember: the drawing operation is always in GL thread instead of
        // main thread
        Message msg = Message.obtain();
        msg.what = MSG_ENDED_DRAWING_FRAME;
        msg.arg1 = mDrawCommand;
        mHandler.sendMessage(msg);
    }

    /**
     * Handle GL surface is changed
     *
     * @param width  surface width
     * @param height surface height
     */
    public void onSurfaceChanged(int width, int height) {
        // recycle bitmap resources if need
        if (mBackgroundBitmap != null) {
            mBackgroundBitmap.recycle();
        }

        if (mBitmap != null) {
            mBitmap.recycle();
        }

        // create bitmap and canvas for page
        //mBackgroundBitmap = background;
        Page page = mPageFlip.getFirstPage();
        int pageW = (int) page.width();
        int pageH = (int) page.height();
        mBitmap = Bitmap.createBitmap(pageW, pageH, Bitmap.Config.ARGB_8888);
        mCanvas.setBitmap(mBitmap);
        mShowContentList = BooksUtil.getShowContent(pageW, pageH, mShowContent);
        mMaxPages = mShowContentList.size();
    }

    /**
     * Handle ended drawing event
     * In here, we only tackle the animation drawing event, If we need to
     * continue requesting render, please return true. Remember this function
     * will be called in main thread
     *
     * @param what event type
     * @return ture if need render again
     */
    public boolean onEndedDrawing(int what) {
        if (what == DRAW_ANIMATING_FRAME) {
            boolean isAnimating = mPageFlip.animating();
            // continue animating
            if (isAnimating) {
                mDrawCommand = DRAW_ANIMATING_FRAME;
                return true;
            }
            // animation is finished
            else {
                // should handle forward flip to update page number and exchange
                // textures between first and second pages. Don't have to handle
                // backward flip since there is no such state happened in double
                // page mode
                if (mPageFlip.getFlipState() == PageFlipState.END_WITH_FORWARD) {
                    final Page first = mPageFlip.getFirstPage();
                    final Page second = mPageFlip.getSecondPage();
                    second.swapTexturesWithPage(first);

                    // update page number for left page
                    if (first.isLeftPage()) {
                        mPageNo -= 2;
                    } else {
                        mPageNo += 2;
                    }
                }

                mDrawCommand = DRAW_FULL_PAGE;
                return true;
            }
        }
        return false;
    }

    /**
     * Draw page content
     *
     * @param number page number
     */
    private void drawPage(int number) {
        final int width = mCanvas.getWidth();
        final int height = mCanvas.getHeight();
        Paint p = new Paint();
        p.setFilterBitmap(true);
        mCanvas.drawColor(Color.parseColor("#FCFBFC"));
        // 1. draw background bitmap
        Bitmap background1 = BitmapFactory.decodeResource(mContext.getResources(), R.drawable.book_left);
        Bitmap background2 = BitmapFactory.decodeResource(mContext.getResources(), R.drawable.book_right);
       // Bitmap background = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        //background.eraseColor(Color.parseColor("#FFFFFF"));//填充颜色
        Rect rect = new Rect(0, 0, width, height);

        if (number % 2 == 0){
            mCanvas.drawBitmap(background2, null, rect, p);
            background2.recycle();
        }else {
            mCanvas.drawBitmap(background1, null, rect, p);
            background1.recycle();
        }


        // 2. draw content
        p.setColor(Color.BLACK);
        p.setAntiAlias(true);
        p.setTextSize(Constants.BOOK_TEXT_SIZE);

        if (number <= mShowContentList.size()) {
            List<String> showLines = mShowContentList.get(number - 1);
            float y = Constants.BOOK_MARGIN_V + Constants.BOOK_TEXT_SIZE;
            for (int i = 0; i < showLines.size(); i++) {
                mCanvas.drawText(showLines.get(i), Constants.BOOK_MARGIN_H, y, p);
                y = y + Constants.BOOK_LINE_SPACE + Constants.BOOK_TEXT_SIZE;
            }
        }

        // 3. draw page number
        p.setTextSize(Constants.PAGE_TEXT_SIZE);
        String text = "";
        if (number < 1) {
            text = "Preface";
        } else if (number > mMaxPages) {
            text = "";
        } else {
            text = "第" + number + "页";
        }

        float textWidth = p.measureText(text);
        float y = height - p.getTextSize();
        if (number % 2 == 0) {
            mCanvas.drawText(text, width - textWidth - 20, y, p);
        } else {
            mCanvas.drawText(text, 20, y, p);
        }

    }

    /**
     * If page can flip forward
     *
     * @return true if it can flip forward
     */
    public boolean canFlipForward() {
        final Page page = mPageFlip.getFirstPage();
        // current page is left page
        if (page.isLeftPage()) {
            return (mPageNo > 1);
        }

        // current page is right page
        return (mPageNo + 2 <= mMaxPages);
    }

    /**
     * Don't need to handle backward flip
     *
     * @return always false
     */
    public boolean canFlipBackward() {
        return false;
    }
}
