package com.ygxj.self.ui.config

import com.tencent.mmkv.MMKV
import com.ygxj.self.AppApplication
import com.ygxj.self.R

/**
 * 用户配置信息
 */
object ConfigManager {
    private val mmkv: MMKV by lazy {
        MMKV.mmkvWithID("ConfigManager")
    }


    /**
     * 是否记住密码
     */
    var rememberPassword: Boolean
        get() = mmkv.decodeBool("rememberPassword", false)
        set(value) {
            mmkv.encode("rememberPassword", value)
        }

    /**
     * 是否第一次配置
     */
    var firstConfig: Boolean
        get() = mmkv.decodeBool("firstConfig", true)
        set(value) {
            mmkv.encode("firstConfig", value)
        }

    /**
     * 主标题
     */
    var title: String?
        get() = mmkv.decodeString("title", AppApplication.Companion.appContext.getString(R.string.app_name))
        set(value) {
            mmkv.encode("title", value)
        }

    /**
     * 副标题
     */
    var subTitle: String?
        get() = mmkv.decodeString("subTitle", "")
        set(value) {
            mmkv.encode("subTitle", value)
        }

    /**
     * 是否显示logo
     */
    var showLogo: Boolean
        get() = mmkv.decodeBool("showLogo", true)
        set(value) {
            mmkv.encode("showLogo", value)
        }

    /**
     * 行业 1中小学，2高校，3成人
     */
    var industry: Int
        get() = mmkv.decodeInt("industry", 1)
        set(value) {
            mmkv.encode("industry", value)
        }

    /**
     * 当前用户是否是超级管理员
     */
    var superAdmin: Boolean
        get() = mmkv.decodeBool("superadmin", false)
        set(value) {
            mmkv.encode("superadmin", value)
        }

    /**
     * 待机时长
     */
    var standbyTime: Int
        get() = mmkv.decodeInt("standbyTime", 20)
        set(value) {
            mmkv.encode("standbyTime", value)
        }

    /**
     * 填写量表的用户年龄
     */
    var userAge: Int = 0

    /**
     * 填写量表的用户性别
     */
    var userSex: String = ""

}