package com.ygxj.self.ui.titbit


import android.R.attr.text
import android.content.Context
import android.content.Intent
import android.graphics.drawable.AnimationDrawable
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.util.Log
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ScreenUtils
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.ygxj.self.R
import com.ygxj.self.data.entity.TitbitEntity
import com.ygxj.self.data.repository.TitbitRepository
import com.ygxj.self.databinding.ActivityCopyBinding
import com.ygxj.self.databinding.ActivityTitbitBinding
import com.ygxj.self.databinding.ItemCenterTitbitBinding
import com.ygxj.self.other.ViewPager2Adapter
import com.ygxj.self.ui.base.BaseActivity
import com.ygxj.self.utils.DateUtil
import timber.log.Timber
import java.util.Locale
import kotlin.jvm.java

/**
 * 中心活动花絮
 */
class TitbitActivity : BaseActivity<ActivityTitbitBinding>(), TextToSpeech.OnInitListener {

    //总数据源
    private var dataList: ArrayList<TitbitEntity> = arrayListOf()

    //实际数据源
    private var titbitList: List<TitbitEntity> = listOf()

    //适配器
    private var viewPager2Adapter: ViewPager2Adapter? = null

    //当前选的的索引
    private var mIndex = 0

    //tts
    private lateinit var tts: TextToSpeech

    //中心布局管理器
    private var centerLayoutManager: CenterLayoutManager? = null

    /**狮子动画**/
    private val animation by lazy {
        binding.ivLionGif.drawable as? AnimationDrawable
    }

    companion object {
        fun start(context: Context, index: Int = 0) {
            val intent = Intent(context, TitbitActivity::class.java)
            intent.putExtra("index", index)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_titbit

    override fun initView() {
        setOnClickListener(R.id.ivBack, R.id.ivLionGif)
        // 初始化TextToSpeech
        tts = TextToSpeech(this, this)
    }

    override fun initData() {
        mIndex = intent.getIntExtra("index", 0)
        //为了实现无限循环，将数据复制10份
        repeat(11) {
            titbitList = TitbitRepository.getCenterTitbitList("", 0, 0)
            dataList.addAll(titbitList)
        }
        //如果没数据直接返回
        if (titbitList.isEmpty()) {
            return
        }
        mIndex += titbitList.size * 5

        initRecyclerView()
        initViewPage()

        dataList.getOrNull(mIndex)?.let {
            it.isSelected = true
            loadData(it)
        }
        binding.rv.models = dataList
        //滚动到指定位置
        if (mIndex > 2) binding.rv.scrollToPosition(mIndex - 2)
        centerLayoutManager?.smoothScrollToPosition(
            binding.rv,
            RecyclerView.State(),
            mIndex
        )
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivBack -> finish()

            R.id.ivLionGif -> {
                if (tts.isSpeaking) {
                    stopSpeak()
                } else {
                    speak(dataList.getOrNull(mIndex)?.content)
                }
            }
        }
    }

    /**
     * 初始化viewpage
     */
    private fun initViewPage() {
        val fragments = arrayListOf<Fragment>()
        for (i in titbitList.indices) {
            val fragment = TitbitDetailFragment.newInstance(titbitList[i].id)
            fragments.add(fragment)
        }
        viewPager2Adapter = ViewPager2Adapter(this, fragments)
        binding.vp.adapter = viewPager2Adapter
        binding.vp.offscreenPageLimit = 3
        binding.vp.setCurrentItem(mIndex % titbitList.size, false)
        binding.vp.isUserInputEnabled = false
    }

    /**
     * 初始化列表
     */
    private fun initRecyclerView() {
        centerLayoutManager = CenterLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        binding.rv.layoutManager = centerLayoutManager
        binding.rv.setup {
            addType<TitbitEntity>(R.layout.item_center_titbit)

            onCreate {
                // 设置每个 item 的宽度，使屏幕刚好显示 5 个 item
                val screenWidth = ScreenUtils.getAppScreenWidth()
                val itemWidth = screenWidth * 0.8 / 5

                val layoutParams = this.itemView.layoutParams
                // ITEM_COUNT是您希望每行显示的项目数量
                layoutParams.width = itemWidth.toInt()
                this.itemView.layoutParams = layoutParams
            }

            onClick(R.id.item) {
                mIndex = layoutPosition
                switchPage()
            }
        }

    }

    /**
     * 切换页面
     */
    private fun switchPage() {
        dataList.forEach { it.isSelected = false }
        dataList[mIndex].isSelected = true
        loadData(dataList[mIndex])
        binding.rv.bindingAdapter.notifyDataSetChanged()
        binding.vp.setCurrentItem(mIndex % titbitList.size, false)
        centerLayoutManager?.smoothScrollToPosition(
            binding.rv,
            RecyclerView.State(),
            mIndex
        )
    }

    /**
     * 加载页面数据
     */
    private fun loadData(data: TitbitEntity) {
        binding.tvTitle.text = data.title
        binding.tvDate.text = DateUtil.timestampToDateTime2(data.time)
        binding.tvNum.text = "【${mIndex % titbitList.size + 1}/${titbitList.size}】"
        stopSpeak()
    }

    /**
     * 初始化tts
     */
    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            // 设置语言为中文
            val result = tts.setLanguage(Locale.CHINESE)
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Timber.e("中文语言不支持")
            } else {
                // 设置播报进度监听器
                tts.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
                    override fun onStart(utteranceId: String) {
                        Timber.e("开始播报")
                        // 播报开始
                        runOnUiThread {
                            resetAutoExitTimer()
                            animation?.start()
                        }
                    }

                    override fun onDone(utteranceId: String) {
                        Timber.e("播报完成")
                        // 播报完成
                        if (mIndex == dataList.size - 1) {
                            mIndex = 0
                        } else {
                            mIndex += 1
                        }
                        runOnUiThread {
                            animation?.stop()
                            switchPage()
                            speak(dataList[mIndex].content)
                        }
                    }

                    override fun onError(utteranceId: String) {
                        // 播报错误
                    }
                })
            }
        } else {
            Toaster.show("语音播报引擎初始化失败")
        }
    }

    /**
     * 开始tts播报
     */
    private fun speak(text: String?) {
        tts.speak(text, TextToSpeech.QUEUE_FLUSH, null, "")
    }

    /**
     * 停止tts播报
     */
    private fun stopSpeak() {
        tts.stop()
        animation?.stop()
    }

    override fun onDestroy() {
        // 释放TextToSpeech资源
        if (::tts.isInitialized) {
            tts.stop()
            tts.shutdown()
        }
        super.onDestroy()
    }


}