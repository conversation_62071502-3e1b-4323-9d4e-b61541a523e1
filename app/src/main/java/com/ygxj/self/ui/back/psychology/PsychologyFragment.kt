package com.ygxj.self.ui.back.psychology


import android.view.View
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.models
import com.drake.brv.utils.mutable
import com.drake.brv.utils.setup
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.github.gzuliyujiang.wheelpicker.DatePicker
import com.github.gzuliyujiang.wheelpicker.entity.DateEntity
import com.qmuiteam.qmui.widget.dialog.QMUIDialog
import com.ygxj.self.R
import com.ygxj.self.data.entity.PsychologyEntity
import com.ygxj.self.data.repository.KnowRepository
import com.ygxj.self.databinding.FragmentPsychologyBinding
import com.ygxj.self.databinding.ItemKnowManageBinding
import com.ygxj.self.ui.back.BackgroundActivity
import com.ygxj.self.ui.back.titbit.DialogTitbitEdit
import com.ygxj.self.ui.base.BaseFragment
import com.ygxj.self.ui.psychology.PsychologyDetailActivity
import com.ygxj.self.utils.DateUtil
import com.ygxj.self.utils.DefaultData

/**
 * 你所不知道的心理学管理页面
 */
class PsychologyFragment : BaseFragment<BackgroundActivity, FragmentPsychologyBinding>() {

    private var mStartTime = 0L

    private var mEndTime = 0L

    private var mPsyList = listOf<PsychologyEntity>()

    override fun getLayoutId(): Int {
        return R.layout.fragment_psychology
    }

    override fun initView() {
        setOnClickListener(
            R.id.btnSearch,
            R.id.btnReset,
            R.id.btnAdd,
            R.id.tvStartTime,
            R.id.tvEndTime,
            R.id.tvType,
        )
        initRecyclerView()
    }

    override fun initData() {
        search()
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.btnSearch -> search()

            R.id.btnReset -> reset()

            R.id.btnAdd -> {
                DialogPsychologyEdit(requireContext(), null) {
                    reset()
                }.build().show()
            }

            R.id.tvStartTime -> selectStartTime()

            R.id.tvEndTime -> selectEndTime()

            R.id.tvType -> selectType()
        }
    }

    /**
     * 选择开始时间
     */
    private fun selectStartTime() {
        hideKeyboard(binding.etSearch)
        val picker = DatePicker(getAttachActivity()!!)
        picker.wheelLayout.setResetWhenLinkage(false)
        picker.wheelLayout.setDateLabel("年", "月", "日")
        picker.wheelLayout.setRange(
            DateEntity.target(1921, 1, 1),
            DateEntity.today(),
            DateEntity.today()
        );
        picker.setOnDatePickedListener { year, month, day ->
            binding.tvStartTime.text = DateUtil.formatToDate(year, month, day)
            mStartTime = DateUtil.dateToTimestamp(binding.tvStartTime.text.toString())
        }
        picker.show()
    }

    /**
     * 选择结束时间
     */
    private fun selectEndTime() {
        hideKeyboard(binding.etSearch)
        val picker = DatePicker(getAttachActivity()!!)
        picker.wheelLayout.setResetWhenLinkage(false)
        picker.wheelLayout.setDateLabel("年", "月", "日")
        picker.wheelLayout.setRange(
            DateEntity.target(1921, 1, 1),
            DateEntity.today(),
            DateEntity.today()
        );
        picker.setOnDatePickedListener { year, month, day ->
            binding.tvEndTime.text = DateUtil.formatToDate(year, month, day)
            mEndTime = DateUtil.dateToTimestamp(binding.tvEndTime.text.toString())
        }
        picker.show()
    }

    /**
     * 选择类别
     */
    private fun selectType(){
        val s = DefaultData.psychologyType
        QMUIDialog.CheckableDialogBuilder(requireContext()).apply {
            setTitle("请选择类别")
            addItems(s) { dialog, which ->
                dialog.dismiss()
                binding.tvType.text = s[which]
            }
            create().show()
        }
    }

    /**
     * 搜索
     */
    private fun search() {
        mPsyList = KnowRepository.getPsychologyList(
            binding.etSearch.text.toString(),
            mStartTime,
            mEndTime,
            binding.tvType.text.toString()
        )
        binding.rv.models = mPsyList
    }

    /**
     * 重置
     */
    private fun reset() {
        binding.etSearch.setText("")
        mStartTime = 0L
        mEndTime = 0L
        binding.tvStartTime.text = ""
        binding.tvEndTime.text = ""
        binding.tvType.text = ""
        search()
    }

    /**
     * 初始化列表
     */
    private fun initRecyclerView() {
        binding.rv.setup {
            addType<PsychologyEntity>(R.layout.item_know_manage)

            onBind {
                val bind = getBinding<ItemKnowManageBinding>()
                bind.tvNo.text = "${layoutPosition + 1}"
            }
            R.id.btnDelete.onClick {
                KnowRepository.deletePsychology(getModel<PsychologyEntity>().id)
                binding.rv.mutable.removeAt(layoutPosition)
                binding.rv.bindingAdapter.notifyDataSetChanged()
            }
            R.id.btnPreview.onClick {
                PsychologyDetailActivity.start(requireContext(), binding.rv.models as ArrayList<PsychologyEntity>, layoutPosition)
            }
            R.id.btnEdit.onClick {
                DialogPsychologyEdit(requireContext(), getModel()) {
                    reset()
                }.build().show()
            }
        }
    }
}