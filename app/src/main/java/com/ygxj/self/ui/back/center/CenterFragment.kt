package com.ygxj.self.ui.back.center


import android.view.View
import com.blankj.utilcode.util.FileUtils
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.models
import com.drake.brv.utils.mutable
import com.drake.brv.utils.setup
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnExternalPreviewEventListener
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.ygxj.self.R
import com.ygxj.self.data.entity.Footer
import com.ygxj.self.data.entity.ImageEntity
import com.ygxj.self.data.repository.CenterRepository
import com.ygxj.self.data.repository.ImageRepository
import com.ygxj.self.databinding.FragmentCenterBinding
import com.ygxj.self.databinding.ItemCenterImageBinding
import com.ygxj.self.ui.back.BackgroundActivity
import com.ygxj.self.ui.base.BaseFragment
import com.ygxj.self.ui.center.CenterIntroActivity
import com.ygxj.self.ui.image.ImagePreviewActivity
import com.ygxj.self.ui.soul.movie.MovieDetailActivity
import com.ygxj.self.utils.DialogUtil
import com.ygxj.self.utils.GlideEngine
import com.ygxj.self.utils.ImageSandboxFileEngine
import com.ygxj.self.utils.loadImage
import com.ygxj.self.utils.show
import kotlin.io.path.Path


/**
 * 中心介绍管理页面
 */
class CenterFragment : BaseFragment<BackgroundActivity, FragmentCenterBinding>() {

    private val mCenterEntity by lazy { CenterRepository.getCenterIntroduction() }
    override fun getLayoutId(): Int {
        return R.layout.fragment_center
    }

    override fun initView() {
        setOnClickListener(R.id.btnSave, R.id.btnPreview)
        initRecyclerView()
    }

    override fun initData() {
        binding.etTitle.setText(mCenterEntity?.title)
        binding.etContent.setText(mCenterEntity?.content)
        binding.rv.models = mCenterEntity?.images
        binding.rv.bindingAdapter.addFooter(Footer())
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.btnSave -> save()

            R.id.btnPreview -> preview()
        }
    }

    /**
     * 初始化列表
     */
    private fun initRecyclerView() {
        binding.rv.setup {
            addType<ImageEntity>(R.layout.item_center_image)

            addType<Footer>(R.layout.item_center_footer)

            onBind {
                if (itemViewType == R.layout.item_center_image) {
                    val bind = getBinding<ItemCenterImageBinding>()
                    val model = getModel<ImageEntity>()
                    bind.ivCover.loadImage(model.path)
                    bind.llMask.show(model.type == 2)
                }
            }

            //预览图片或视频
            R.id.item.onClick {
                val model = getModel<ImageEntity>()
                if (model.type == 1) {
                    ImagePreviewActivity.start(requireContext(), listOf(model.path))
                } else {
                    MovieDetailActivity.start(requireContext(), model.path)
                }
            }
            //删除
            R.id.iv_delete.onClick {
                val model = getModel<ImageEntity>()
                FileUtils.delete(model.path)
                ImageRepository.deleteImage(model)
                binding.rv.mutable.removeAt(layoutPosition)
                binding.rv.bindingAdapter.notifyItemRemoved(layoutPosition)
            }
            //添加图片
            R.id.btn_add_image.onClick {
                selectImage()
            }
            //添加视频
            R.id.btn_add_video.onClick {
                selectVideo()
            }
        }
    }

    /**
     * 选择图片
     */
    private fun selectImage() {
        PictureSelector.create(this)
            .openGallery(SelectMimeType.ofImage())
            .setImageEngine(GlideEngine.createGlideEngine())
            .setSandboxFileEngine(ImageSandboxFileEngine())
            .isDisplayCamera(false)
            .forResult(object : OnResultCallbackListener<LocalMedia?> {
                override fun onResult(result: ArrayList<LocalMedia?>?) {
                    result?.forEach { localMedia ->
                        val imageEntity = ImageEntity(1, localMedia?.availablePath.toString())
                        binding.rv.bindingAdapter.addModels(listOf(imageEntity))
                    }
                }

                override fun onCancel() {}
            })
    }

    /**
     * 选择视频
     */
    private fun selectVideo() {
        PictureSelector.create(this)
            .openGallery(SelectMimeType.ofVideo())
            .setImageEngine(GlideEngine.createGlideEngine())
            .setSandboxFileEngine(ImageSandboxFileEngine())
            .isDisplayCamera(false)
            .forResult(object : OnResultCallbackListener<LocalMedia?> {
                override fun onResult(result: ArrayList<LocalMedia?>?) {
                    result?.forEach { localMedia ->
                        val imageEntity = ImageEntity(2, localMedia?.availablePath.toString())
                        binding.rv.bindingAdapter.addModels(listOf(imageEntity))
                    }
                }

                override fun onCancel() {}
            })
    }

    /**
     * 保存
     */
    private fun save() {
        if (binding.etTitle.text.isNullOrBlank()) {
            Toaster.show("请输入标题")
            return
        }
        if (binding.etContent.text.isNullOrBlank()) {
            Toaster.show("请输入内容")
            return
        }
        mCenterEntity?.images?.forEach {
            it.delete()
        }
        val imageList = binding.rv.mutable as ArrayList<ImageEntity>
        //先保存图片
        imageList.forEach { ImageRepository.saveImage(it) }
        mCenterEntity?.title = binding.etTitle.text.toString()
        mCenterEntity?.content = binding.etContent.text.toString()
        mCenterEntity?.images = imageList
        //保存中心介绍
        CenterRepository.saveCenterIntroduction(mCenterEntity)
        DialogUtil.showAutoDismissSuccessDialog(requireContext(), "保存成功")
    }

    /**
     * 预览
     */
    private fun preview() {
        if (binding.etTitle.text.isNullOrBlank()) {
            Toaster.show("请输入标题")
            return
        }
        if (binding.etContent.text.isNullOrBlank()) {
            Toaster.show("请输入内容")
            return
        }
        CenterIntroActivity.start(
            requireContext(),
            binding.etTitle.text.toString(),
            binding.etContent.text.toString(),
            binding.rv.mutable as ArrayList<ImageEntity>
        )
    }
}