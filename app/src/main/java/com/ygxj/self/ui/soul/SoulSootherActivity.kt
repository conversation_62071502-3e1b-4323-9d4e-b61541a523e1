package com.ygxj.self.ui.soul


import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.lzf.easyfloat.EasyFloat
import com.qmuiteam.qmui.kotlin.onClick
import com.ygxj.self.R
import com.ygxj.self.data.entity.RelaxEntity
import com.ygxj.self.data.repository.RelaxRepository
import com.ygxj.self.databinding.ActivitySoulSootherBinding
import com.ygxj.self.databinding.ItemSoulSootherBinding
import com.ygxj.self.ui.base.BaseActivity
import com.ygxj.self.ui.soul.image.ImageDetailActivity
import com.ygxj.self.ui.soul.movie.MovieDetailActivity
import com.ygxj.self.ui.soul.music.MusicDetailActivity
import com.ygxj.self.utils.DoubleClickUtils
import com.ygxj.self.utils.loadImage
import com.ygxj.self.utils.show
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 心灵鸡汤
 */
class SoulSootherActivity : BaseActivity<ActivitySoulSootherBinding>() {

    private var job: Job? = null
    override fun getLayoutId(): Int = R.layout.activity_soul_soother

    override fun initView() {
        setOnClickListener(R.id.clRoot, R.id.ivBack, R.id.ibMovie, R.id.ibMusic, R.id.ibPicture)
        initRecyclerView()
    }

    override fun initData() {
        setTabSelected(1)
    }

    /**
     * 初始化列表
     */
    private fun initRecyclerView() {
        val layoutManager = FlexboxLayoutManager(this).apply {
            flexDirection = FlexDirection.COLUMN
            flexWrap = FlexWrap.WRAP
        }
        binding.rv.layoutManager = layoutManager

        // 监听滑动状态  滑动过程中暂停图片加载，停止滑动时恢复图片加载，避免滑动卡顿问题
        binding.rv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                when (newState) {
                    RecyclerView.SCROLL_STATE_IDLE -> {
                        // 停止滑动时恢复图片加载
                        Glide.with(this@SoulSootherActivity).resumeRequests()
                    }
                    RecyclerView.SCROLL_STATE_DRAGGING,
                    RecyclerView.SCROLL_STATE_SETTLING -> {
                        // 开始滑动时暂停图片加载
                        Glide.with(this@SoulSootherActivity).pauseRequests()
                    }
                }
            }
        })

        binding.rv.setup {
            addType<RelaxEntity>(R.layout.item_soul_soother)

            onBind {
                val bind = getBinding<ItemSoulSootherBinding>()
                bind.startSpace.show((layoutPosition + 1) % 2 == 0)
                bind.topSpace.show((layoutPosition + 1) % 4 == 0)
                bind.tvMovie.text = getModel<RelaxEntity>().name
                bind.ivMovie.loadImage(getModel<RelaxEntity>().image)
            }

            onFastClick(R.id.ivMovie){
                val entity = getModel<RelaxEntity>()
                if (DoubleClickUtils.isOnDoubleClick(200)) {
                    job?.cancel()
                    EasyFloat.dismiss()
                    goToDetail(entity, layoutPosition)
                } else {
                    //单击
                    job = lifecycleScope.launch {
                        delay(200)
                        if (EasyFloat.isShow()) {
                            EasyFloat.getFloatView()?.let {
                                loadTipViewData(it, entity, layoutPosition)
                            }
                        } else {
                            EasyFloat.with(this@SoulSootherActivity)
                                .setLayout(R.layout.layout_movie_info) {
                                    loadTipViewData(it, entity, layoutPosition)
                                }
                                .setImmersionStatusBar(true)
                                .setGravity(Gravity.CENTER)
                                .setAnimator(null)
                                .show()
                        }
                    }
                }
            }
        }
    }

    /**
     * 加载弹窗数据
     */
    private fun loadTipViewData(tip: View, entity: RelaxEntity, position: Int) {
        tip.findViewById<TextView>(R.id.tvTitle).text = entity.name
        if (entity.info.isBlank()) entity.info = "无"
        tip.findViewById<TextView>(R.id.tvContent).text = entity.info

        tip.findViewById<ImageView>(R.id.ivClose).onClick {
            EasyFloat.dismiss()
        }
        tip.findViewById<TextView>(R.id.tvPlay).onClick {
            EasyFloat.dismiss()
            goToDetail(entity, position)
        }
    }

    //跳转详情
    private fun goToDetail(entity: RelaxEntity, position: Int) {
        when (entity.type) {
            1 -> MovieDetailActivity.start(this, entity.path,entity.name)

            2 -> MusicDetailActivity.start(this, position)

            3 -> ImageDetailActivity.start(this, position)
        }
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.clRoot -> EasyFloat.dismiss()

            R.id.ivBack -> finish()

            R.id.ibMovie -> setTabSelected(1)

            R.id.ibMusic -> setTabSelected(2)

            R.id.ibPicture -> setTabSelected(3)
        }
    }

    /**
     * 标签选中
     */
    private fun setTabSelected(type: Int) {
        when(type){
            1 -> getRelaxMovieData()

            2 -> getRelaxMusicData()

            3 -> getRelaxPictureData()
        }
        binding.ibMovie.isSelected = type == 1
        binding.ibMusic.isSelected = type == 2
        binding.ibPicture.isSelected = type == 3
        binding.rv.scrollToPosition(0)
    }

    /**
     * 获取视频数据
     */
    private fun getRelaxMovieData() {
        val list = RelaxRepository.getRelaxMovieData()
        binding.rv.models = list
        binding.tvNum.text = "共${list.size}条"
    }

    /**
     * 获取音乐数据
     */
    private fun getRelaxMusicData() {
        val list = RelaxRepository.getRelaxMusicData()
        binding.rv.models = list
        binding.tvNum.text = "共${list.size}条"
    }

    /**
     * 获取图片数据
     */
    private fun getRelaxPictureData() {
        val list = RelaxRepository.getRelaxPictureData()
        binding.rv.models = list
        binding.tvNum.text = "共${list.size}条"
    }

    override fun onDestroy() {
        super.onDestroy()
        EasyFloat.dismiss()
        job?.cancel()
    }
}