package com.ygxj.self.ui.main


import android.graphics.drawable.AnimationDrawable
import android.view.View
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.ygxj.self.R
import com.ygxj.self.databinding.ActivityMainBinding
import com.ygxj.self.ui.base.BaseActivity
import com.ygxj.self.ui.center.CenterIntroActivity
import com.ygxj.self.ui.login.LoginActivity
import com.ygxj.self.ui.psychology.PsychologyActivity
import com.ygxj.self.ui.soul.SoulSootherActivity
import com.ygxj.self.ui.titbit.TitbitActivity

import com.ygxj.self.utils.DoubleClickUtils
import timber.log.Timber
import kotlin.jvm.java


/**
 * 主页面
 */
class MainActivity : BaseActivity<ActivityMainBinding>() {
    /**背景动画**/
    private val animationBg by lazy { binding.ivBg.drawable as? AnimationDrawable }

    /**蜻蜓动画**/
    private val animationDragonfly by lazy { binding.ivDragonfly.drawable as? AnimationDrawable }

    override fun getLayoutId(): Int = R.layout.activity_main

    override fun initView() {
        setOnClickListener(
            R.id.ivMenu1,
            R.id.ivMenu2,
            R.id.ivMenu3,
            R.id.ivMenu4,
            R.id.ivMenu5,
            R.id.ivMenu6,
            R.id.ivMenu7,
            R.id.ivMenu8,
            R.id.ivMenu9,
            R.id.ivEnterManager
        )
        animationBg?.start()
        animationDragonfly?.start()
    }

    override fun initData() {

    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivMenu1 -> startActivity(CenterIntroActivity::class.java)

            R.id.ivMenu2 -> startActivity(TitbitActivity::class.java)

            R.id.ivMenu3 -> startActivity(PsychologyActivity::class.java)

            R.id.ivMenu4 -> startActivity(SoulSootherActivity::class.java)

            R.id.ivMenu5 -> {
                Toaster.show("心灵鸡汤")
            }

            R.id.ivMenu6 -> {
                Toaster.show("心灵鸡汤")
            }

            R.id.ivMenu7 -> {
                Toaster.show("心灵鸡汤")
            }

            R.id.ivMenu8 -> {
                Toaster.show("心灵鸡汤")
            }

            R.id.ivMenu9 -> {
                Toaster.show("心灵鸡汤")
            }

            R.id.ivEnterManager -> startActivity(LoginActivity::class.java)
        }
    }


    override fun onBackPressed() {
        if (!DoubleClickUtils.isOnDoubleClick()) {
            Toaster.show("再按一次退出")
            return
        }
        super.onBackPressed()

    }

}