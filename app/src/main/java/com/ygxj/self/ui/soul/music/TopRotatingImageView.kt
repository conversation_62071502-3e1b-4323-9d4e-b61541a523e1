package com.ygxj.self.ui.soul.music

import android.animation.ObjectAnimator
import android.view.View
import android.widget.ImageView

/**
 * 以顶部为圆心，旋转一定的角度 ImageView
 */
class TopRotatingImageView(private val imageView: ImageView) {

    init {
        // 设置旋转中心为ImageView顶部中央
        imageView.pivotX = imageView.width / 2f
        imageView.pivotY = 0f
        // 如果你在onCreate或onViewCreated中实例化，需要用post，确保宽高已知
        imageView.post {
            imageView.pivotX = imageView.width / 2f
            imageView.pivotY = 0f
        }
    }

    // 旋转到目标角度
    fun rotateTo(angle: Float, duration: Long = 300L) {
        ObjectAnimator.ofFloat(imageView, View.ROTATION, imageView.rotation, angle)
            .apply { this.duration = duration }
            .start()
    }

    // 回到原始位置
    fun reset(duration: Long = 300L) {
        ObjectAnimator.ofFloat(imageView, View.ROTATION, imageView.rotation, 0f)
            .apply { this.duration = duration }
            .start()
    }
}