package com.ygxj.self.ui.psychology


import android.view.View
import androidx.databinding.DataBindingUtil.getBinding
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.drake.brv.utils.models
import com.drake.brv.utils.mutable
import com.drake.brv.utils.setup
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.ygxj.self.R
import com.ygxj.self.data.entity.PsychologyEntity
import com.ygxj.self.data.repository.KnowRepository
import com.ygxj.self.databinding.ActivityCopyBinding
import com.ygxj.self.databinding.ActivityPsychologyBinding
import com.ygxj.self.databinding.ItemPsychologyBinding
import com.ygxj.self.ui.base.BaseActivity
import com.ygxj.self.utils.gone

/**
 * 你不知道的心理学
 */
class PsychologyActivity : BaseActivity<ActivityPsychologyBinding>() {

    override fun getLayoutId(): Int = R.layout.activity_psychology

    override fun initView() {
        setOnClickListener(
            R.id.btnPsyItem1,
            R.id.btnPsyItem2,
            R.id.btnPsyItem3,
            R.id.btnPsyItem4,
            R.id.ivPre,
            R.id.ivNext,
            R.id.ivBack
        )
        initRecyclerView()
        setTabSelected(1)
    }

    override fun initData() {

    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivBack -> finish()

            R.id.btnPsyItem1 -> setTabSelected(1)

            R.id.btnPsyItem2 -> setTabSelected(2)

            R.id.btnPsyItem3 -> setTabSelected(3)

            R.id.btnPsyItem4 -> setTabSelected(4)

            R.id.ivPre -> preItem()

            R.id.ivNext -> nextItem()
        }
    }

    /**
     * 设置tab选中
     */
    private fun setTabSelected(index: Int) {
        binding.btnPsyItem1.isSelected = index == 1
        binding.btnPsyItem2.isSelected = index == 2
        binding.btnPsyItem3.isSelected = index == 3
        binding.btnPsyItem4.isSelected = index == 4
        binding.rv.models = KnowRepository.getPsychologyData(index)
        binding.tvNum.text = "共${binding.rv.mutable.size}条"
        if (binding.rv.mutable.isNotEmpty()){
            binding.rv.scrollToPosition(0)
            binding.ivPre.gone()
        }
    }

    /**
     * 初始化列表
     */
    private fun initRecyclerView() {
        binding.rv.setup {
            addType<PsychologyEntity>(R.layout.item_psychology)

            onClick(R.id.item){
                PsychologyDetailActivity.start(this@PsychologyActivity, binding.rv.models as ArrayList<PsychologyEntity>, layoutPosition)
            }
        }
        //监听滚动
        binding.rv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                when (newState) {
                    RecyclerView.SCROLL_STATE_IDLE -> {
                        // RecyclerView 停止滑动
                        val layoutManager =
                            binding.rv.layoutManager as LinearLayoutManager
                        val firstCompletelyVisibleItemPosition =
                            layoutManager.findFirstCompletelyVisibleItemPosition()
                        val lastCompletelyVisibleItemPosition =
                            layoutManager.findLastCompletelyVisibleItemPosition()
                        if (firstCompletelyVisibleItemPosition >0) {
                            binding.ivPre.visibility = View.VISIBLE
                        } else {
                            binding.ivPre.visibility = View.GONE
                        }
                        if (lastCompletelyVisibleItemPosition == binding.rv.mutable.size - 1) {
                            binding.ivNext.visibility = View.GONE
                        } else {
                            binding.ivNext.visibility = View.VISIBLE
                        }
                    }
                }
            }
        })
    }

    /**
     * 上一个
     */
    private fun preItem() {
        binding.rv.post {
            //获取第一个完整可见的item位置
            val layoutManager = binding.rv.layoutManager as LinearLayoutManager
            val firstCompletelyVisibleItemPosition =
                layoutManager.findFirstCompletelyVisibleItemPosition()
            val index = if (firstCompletelyVisibleItemPosition == 0) {
                0
            } else {
                firstCompletelyVisibleItemPosition - 1
            }
            binding.rv.smoothScrollToPosition(index)
        }
    }

    /**
     * 下一个
     */
    private fun nextItem() {
        binding.rv.post {
            //获取最后一个完整可见的item位置
            val layoutManager = binding.rv.layoutManager as LinearLayoutManager
            val lastCompletelyVisibleItemPosition =
                layoutManager.findLastCompletelyVisibleItemPosition()
            binding.rv.smoothScrollToPosition(lastCompletelyVisibleItemPosition + 1)
        }
    }
}