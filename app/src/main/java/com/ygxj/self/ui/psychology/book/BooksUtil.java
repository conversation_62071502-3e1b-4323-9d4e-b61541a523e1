package com.ygxj.self.ui.psychology.book;

import android.graphics.Paint;

import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

public class BooksUtil {

    //将要展示的内容处理好
    public static List<List<String>> getShowContent(int w, int h, String content) {
        // 处理 null 或空内容的情况
        if (content == null || content.isEmpty()) {
            return new ArrayList<>(); // 返回空列表而不是 null
        }
        ArrayList<String> lines = new ArrayList<>();
        Paint paint = new Paint();
        paint.setFilterBitmap(true);
        paint.setTextSize(Constants.BOOK_TEXT_SIZE);
        int wordWidth = (int) paint.measureText("\u3000");
        int wordCount = (w-Constants.BOOK_MARGIN_H*2) / wordWidth;
        String contentReplaceString = content.replaceAll("\n","\n\u3000\u3000");
        contentReplaceString = contentReplaceString.replaceAll("\u0000","");
        StringBuilder sb = new StringBuilder();
        sb.append("\u3000\u3000");
        for (int i = 0; i < contentReplaceString.length(); i++) {
            char c = contentReplaceString.charAt(i);
            if (String.valueOf(c).equals("\n")) {
                lines.add(sb.toString());
                sb.setLength(0);
            } else {
                sb.append(c);
                if (sb.length() == wordCount) {
                    lines.add(sb.toString());
                    sb.setLength(0);
                }
            }
        }

        lines.add(sb.toString());
        sb.setLength(0);


        return Lists.partition(lines, getMaxLineEachColumn(h));
    }


    //每页有多少行
    public static int getMaxLineEachColumn(int viewHeight) {
        //字体大小加上行间距
        int lineHeight = Constants.BOOK_TEXT_SIZE + Constants.BOOK_LINE_SPACE;
        //总高度减去上下间距
        int usefulHeight = viewHeight - Constants.BOOK_MARGIN_V*2;
        //页面一共有多少行
        return usefulHeight / lineHeight;
    }
}
