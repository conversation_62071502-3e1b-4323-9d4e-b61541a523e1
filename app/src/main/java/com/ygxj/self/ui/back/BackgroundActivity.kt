package com.ygxj.self.ui.back


import android.content.ComponentName
import android.content.Intent
import android.content.pm.PackageManager
import android.view.View
import com.blankj.utilcode.util.FragmentUtils
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.ygxj.self.R
import com.ygxj.self.databinding.ActivityBackgroundBinding
import com.ygxj.self.databinding.ActivityCopyBinding
import com.ygxj.self.other.ActivityManager
import com.ygxj.self.ui.back.center.CenterFragment
import com.ygxj.self.ui.back.psychology.PsychologyFragment
import com.ygxj.self.ui.back.titbit.TitbitFragment
import com.ygxj.self.ui.config.ConfigManager
import com.ygxj.self.ui.base.BaseActivity
import com.ygxj.self.ui.config.ConfigActivity
import com.ygxj.self.utils.gone
import com.ygxj.self.utils.show
import com.ygxj.self.utils.visible
import kotlin.jvm.java

/**
 * 后台主页面
 */
class BackgroundActivity : BaseActivity<ActivityBackgroundBinding>() {

    //fragment 列表
    private var mFragments = listOf(CenterFragment(), TitbitFragment(), PsychologyFragment())

    override fun getLayoutId(): Int = R.layout.activity_background

    override fun initView() {
        setOnClickListener(
            R.id.btnBackToFront,
            R.id.btnEditPwd,
            R.id.btnAlarmClock,
            R.id.btnChangeInfo,
            R.id.btnBackToDesktop
        )
        getAlarmClockPermissions()
        FragmentUtils.replace(supportFragmentManager, mFragments[0], R.id.flContainer)
    }

    override fun initData() {
        //监听tab变化
        binding.tabLayout.observeIndexChange { fromIndex: Int, toIndex: Int, reselect: Boolean, fromUser: Boolean ->
            if (!reselect && toIndex < mFragments.size) {
                FragmentUtils.replace(supportFragmentManager, mFragments[toIndex], R.id.flContainer)
            }
        }
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.btnBackToFront -> finish()

            R.id.btnEditPwd -> DialogEditPwd(view.context).build().show()

            R.id.btnAlarmClock -> {
                //跳转系统设置界面
                intent.setClassName("ys.os.tools", "ys.os.tools.MainActivity")
                startActivity(intent)
            }

            R.id.btnChangeInfo -> ConfigActivity.start(this@BackgroundActivity, true)

            R.id.btnBackToDesktop -> ActivityManager.getInstance().finishAllActivities()
        }
    }

    override fun onResume() {
        super.onResume()
        binding.tvTitle.text = ConfigManager.title
        binding.ivLogo.show(ConfigManager.showLogo)
        binding.llChangeInfo.show(ConfigManager.superAdmin)
    }

    /**
     * 判断定时关机按钮是否展示
     * 跳转到系统设置界面 设置定时开关机
     */
    private fun getAlarmClockPermissions() {
        val packageName = "ys.os.tools" // 包名
        val activityName = "ys.os.tools.MainActivity" //  Activity 的完整路径
        val packageManager = packageManager
        val componentName = ComponentName(packageName, activityName)
        val intent = Intent().setComponent(componentName)

        val resolveInfo = packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)

        if (resolveInfo != null && resolveInfo.activityInfo.exported) {
            binding.llAlarmClock.visible()
        } else {
            binding.llAlarmClock.gone()
        }
    }
}