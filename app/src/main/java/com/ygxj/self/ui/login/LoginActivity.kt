package com.ygxj.self.ui.login


import android.text.Editable
import android.text.TextWatcher
import android.view.View
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.ygxj.self.BuildConfig
import com.ygxj.self.R
import com.ygxj.self.data.repository.UserRepository
import com.ygxj.self.databinding.ActivityLoginBinding
import com.ygxj.self.ui.config.ConfigManager
import com.ygxj.self.ui.back.BackgroundActivity
import com.ygxj.self.ui.base.BaseActivity
import com.ygxj.self.utils.DefaultData
import kotlin.jvm.java


/**
 * 登录页面
 */
class LoginActivity : BaseActivity<ActivityLoginBinding>(), TextWatcher {

    override fun getLayoutId(): Int = R.layout.activity_login

    override fun initView() {
        setOnClickListener(R.id.btnLogin, R.id.btnCancel)
        binding.cbRemember.isChecked = ConfigManager.rememberPassword
        binding.cbRemember.setOnCheckedChangeListener { _, isChecked ->
            ConfigManager.rememberPassword = isChecked
        }
        binding.etAccount.addTextChangedListener(this)
    }

    override fun initData() {
        if (BuildConfig.DEBUG) {
            binding.etAccount.setText("superadmin")
            binding.etPassword.setText("ygxj@123456")
        }
    }


    /**
     * 按钮点击事件
     */
    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.btnCancel -> finish()

            R.id.btnLogin -> login()
        }
    }

    /**
     * 登录
     */
    private fun login() {
        if (binding.etAccount.text.isNullOrBlank()) {
            Toaster.show("请输入账号")
            return
        }
        if (binding.etPassword.text.isNullOrBlank()) {
            Toaster.show("请输入密码")
            return
        }
        val user = UserRepository.getUserByAccount(binding.etAccount.text.toString())
        if (user == null) {
            Toaster.show("用户不存在")
            return
        }
        if (user.password != binding.etPassword.text.toString()) {
            Toaster.show("密码错误")
            return
        }
        //判断是否是超级管理员
        if (user.account == DefaultData.superAdmin.account) {
            ConfigManager.superAdmin = true
        } else {
            ConfigManager.superAdmin = false
        }
        startActivity(BackgroundActivity::class.java)
        finish()
    }

    //监听账号输入
    override fun afterTextChanged(p0: Editable?) {
        if (ConfigManager.rememberPassword) {
            if (binding.etAccount.text.toString() == DefaultData.admin.account) {
                val user = UserRepository.getUserByAccount(DefaultData.admin.account)
                binding.etPassword.setText(user?.password)
            }
            if (binding.etAccount.text.toString() == DefaultData.superAdmin.account) {
                val user = UserRepository.getUserByAccount(DefaultData.superAdmin.account)
                binding.etPassword.setText(user?.password)
            }
        }
    }

    override fun beforeTextChanged(
        p0: CharSequence?,
        p1: Int,
        p2: Int,
        p3: Int
    ) {

    }

    override fun onTextChanged(
        p0: CharSequence?,
        p1: Int,
        p2: Int,
        p3: Int
    ) {

    }

}