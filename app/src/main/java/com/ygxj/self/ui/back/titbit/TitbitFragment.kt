package com.ygxj.self.ui.back.titbit


import android.text.Selection.moveUp
import android.view.View
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import com.drake.brv.BindingAdapter
import com.drake.brv.listener.DefaultItemTouchCallback
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.models
import com.drake.brv.utils.mutable
import com.drake.brv.utils.setup
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.github.gzuliyujiang.wheelpicker.DatePicker
import com.github.gzuliyujiang.wheelpicker.entity.DateEntity
import com.ygxj.self.R
import com.ygxj.self.data.entity.ImageEntity
import com.ygxj.self.data.entity.TitbitEntity
import com.ygxj.self.data.repository.TitbitRepository
import com.ygxj.self.databinding.FragmentCopyBinding
import com.ygxj.self.databinding.FragmentTitbitBinding
import com.ygxj.self.databinding.ItemTitbitManageBinding
import com.ygxj.self.ui.back.BackgroundActivity
import com.ygxj.self.ui.base.BaseFragment
import com.ygxj.self.ui.main.MainActivity
import com.ygxj.self.ui.titbit.TitbitActivity
import com.ygxj.self.utils.DateUtil
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Collections

/**
 * 中心活动花絮管理页面
 */
class TitbitFragment : BaseFragment<BackgroundActivity, FragmentTitbitBinding>() {

    private var mStartTime = 0L

    private var mEndTime = 0L

    private var mTitbitList = listOf<TitbitEntity>()

    override fun getLayoutId(): Int {
        return R.layout.fragment_titbit
    }

    override fun initView() {
        setOnClickListener(
            R.id.btnSearch,
            R.id.btnReset,
            R.id.btnAdd,
            R.id.tvStartTime,
            R.id.tvEndTime
        )
        initRecyclerView()
    }

    override fun initData() {
        search()
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.btnSearch -> search()

            R.id.btnReset -> reset()

            R.id.btnAdd -> {
                DialogTitbitEdit(requireContext(), null) {
                    reset()
                }.build().show()
            }

            R.id.tvStartTime -> selectStartTime()

            R.id.tvEndTime -> selectEndTime()
        }
    }

    /**
     * 选择开始时间
     */
    private fun selectStartTime() {
        hideKeyboard(binding.etSearch)
        val picker = DatePicker(getAttachActivity()!!)
        picker.wheelLayout.setResetWhenLinkage(false)
        picker.wheelLayout.setDateLabel("年", "月", "日")
        picker.wheelLayout.setRange(
            DateEntity.target(1921, 1, 1),
            DateEntity.today(),
            DateEntity.today()
        );
        picker.setOnDatePickedListener { year, month, day ->
            binding.tvStartTime.text = DateUtil.formatToDate(year, month, day)
            mStartTime = DateUtil.dateToTimestamp(binding.tvStartTime.text.toString())
        }
        picker.show()
    }

    /**
     * 选择结束时间
     */
    private fun selectEndTime() {
        hideKeyboard(binding.etSearch)
        val picker = DatePicker(getAttachActivity()!!)
        picker.wheelLayout.setResetWhenLinkage(false)
        picker.wheelLayout.setDateLabel("年", "月", "日")
        picker.wheelLayout.setRange(
            DateEntity.target(1921, 1, 1),
            DateEntity.today(),
            DateEntity.today()
        );
        picker.setOnDatePickedListener { year, month, day ->
            binding.tvEndTime.text = DateUtil.formatToDate(year, month, day)
            mEndTime = DateUtil.dateToTimestamp(binding.tvEndTime.text.toString())
        }
        picker.show()
    }

    /**
     * 搜索
     */
    private fun search() {
        mTitbitList = TitbitRepository.getCenterTitbitList(
            binding.etSearch.text.toString(),
            mStartTime,
            mEndTime
        )
        binding.rv.models = mTitbitList
    }

    /**
     * 重置
     */
    private fun reset() {
        binding.etSearch.setText("")
        mStartTime = 0L
        mEndTime = 0L
        binding.tvStartTime.text = ""
        binding.tvEndTime.text = ""
        search()
    }

    /**
     * 初始化列表
     */
    private fun initRecyclerView() {
        binding.rv.setup {
            addType<TitbitEntity>(R.layout.item_titbit_manage)
            //拖拽排序
            itemTouchHelper = mItemTouchHelper

            onBind {
                val bind = getBinding<ItemTitbitManageBinding>()
                bind.tvNo.text = "${layoutPosition + 1}"
            }

            R.id.btnUp.onClick {
                if (mTitbitList.size <= 1) return@onClick
                moveUp(layoutPosition)
            }
            R.id.btnDown.onClick {
                moveDown(layoutPosition)
            }
            R.id.btnDelete.onClick {
                TitbitRepository.deleteCenterTitbit(getModel<TitbitEntity>().id)
                binding.rv.mutable.removeAt(layoutPosition)
                refreshData()
            }
            R.id.btnPreview.onClick {
                TitbitActivity.start(requireContext(), layoutPosition)
            }
            R.id.btnEdit.onClick {
                DialogTitbitEdit(requireContext(), getModel()) {
                    reset()
                }.build().show()
            }
        }
    }

    //拖拽排序
    private val mItemTouchHelper = ItemTouchHelper(object : DefaultItemTouchCallback() {
        //当拖拽动作完成且松开手指时触发
        override fun onDrag(
            source: BindingAdapter.BindingViewHolder,
            target: BindingAdapter.BindingViewHolder
        ) {
            super.onDrag(source, target)
            lifecycleScope.launch {
                delay(300)
                refreshData()
            }
        }
    })

    //上移
    private fun moveUp(position: Int) {
        if (mTitbitList.size <= 1) return
        if (position == 0) {
            Collections.swap(mTitbitList, position, mTitbitList.size - 1)
        } else {
            Collections.swap(mTitbitList, position, position - 1)
        }
        refreshData()
    }

    //下移
    private fun moveDown(position: Int) {
        if (mTitbitList.size <= 1) return
        if (position == mTitbitList.size - 1) {
            Collections.swap(mTitbitList, position, 0)
        } else {
            Collections.swap(mTitbitList, position, position + 1)
        }
        refreshData()
    }

    //刷新列表
    private fun refreshData() {
        mTitbitList.forEachIndexed { index, titbitEntity ->
            titbitEntity.sort = index + 1
            titbitEntity.save()
        }
        binding.rv.bindingAdapter.notifyDataSetChanged()
    }
}