package com.ygxj.self.ui.soul.music

import android.animation.ObjectAnimator
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import android.widget.RelativeLayout

/**
 * 360度旋转的 View
 */
class RotatingView(private val view: RelativeLayout) {
    private val animator = ObjectAnimator.ofFloat(view, View.ROTATION, 0f, 360f).apply {
        duration = 4000L
        repeatCount = ObjectAnimator.INFINITE
        repeatMode = ObjectAnimator.RESTART
        interpolator = LinearInterpolator() // 保证速度恒定
    }

    fun start() {
        if (!animator.isStarted) animator.start()
        else animator.resume()
    }

    fun pause() {
        animator.pause()
    }

    fun cancel() {
        animator.cancel()
    }
}