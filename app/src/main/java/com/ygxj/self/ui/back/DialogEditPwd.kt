package com.ygxj.self.ui.back

import android.content.Context
import androidx.databinding.DataBindingUtil
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.ygxj.self.R
import com.ygxj.self.data.repository.UserRepository
import com.ygxj.self.databinding.DialogEditPwdBinding
import com.ygxj.self.ui.config.ConfigManager
import com.ygxj.self.utils.DefaultData

/**
 * 修改管理员密码弹框
 */
class DialogEditPwd(context: Context) : CenterPopupView(context) {

    private lateinit var binding: DialogEditPwdBinding

    override fun getImplLayoutId() = R.layout.dialog_edit_pwd

    override fun onCreate() {
        super.onCreate()
        binding = DataBindingUtil.bind(popupImplView)!!

        binding.btnCancel.setOnClickListener { dismiss() }
        binding.btnConfirm.setOnClickListener {
            val oldPwd = binding.etOldPwd.text.toString()
            val newPwd = binding.etNewPwd.text.toString()
            val newPwdConfirm = binding.etNewPwdConfirm.text.toString()
            if (oldPwd.isBlank() || newPwd.isBlank() || newPwdConfirm.isBlank()) return@setOnClickListener

            val user = if (ConfigManager.superAdmin) {
                UserRepository.getUserByAccount(DefaultData.superAdmin.account)
            } else {
                UserRepository.getUserByAccount(DefaultData.admin.account)
            }
            if (user == null) return@setOnClickListener
            if (oldPwd != user.password) {
                Toaster.show("原密码输入不正确")
                return@setOnClickListener
            }
            if (newPwd.length < 6 || newPwdConfirm.length < 6) {
                Toaster.show("新密码不能少于6位")
                return@setOnClickListener
            }
            if (newPwd != newPwdConfirm) {
                Toaster.show("两次输入的密码不一致")
                return@setOnClickListener
            }
            user.password = newPwd
            UserRepository.updateUserData(user)
            Toaster.show("密码修改成功，下次登录请使用新密码")
            dismiss()
        }
    }

    fun build(): BasePopupView {
        return XPopup.Builder(context)
            .hasStatusBar(false)
            .isViewMode(true)
            .moveUpToKeyboard(true)
            .asCustom(this)
    }
}