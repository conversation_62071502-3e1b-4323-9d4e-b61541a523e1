package com.ygxj.self.data.repository

import android.content.ContentValues
import com.ygxj.self.data.entity.PsychologyEntity
import org.litepal.LitePal

object KnowRepository {

    /**
     * 判断你所不知道的心理学是否为空
     */
    fun isEmptyPsychology(): Boolean = LitePal.findAll(PsychologyEntity::class.java).isNullOrEmpty()

    /**
     * 保存你所不知道的心理学
     */
    fun savePsychologies(psychologies: List<PsychologyEntity>) = LitePal.saveAll(psychologies)

    /**
     * 保存你所不知道的心理学
     */
    fun savePsychology(titbit: PsychologyEntity): Boolean = titbit.save()

    /**
     * 修改你所不知道的心理学
     */
    fun updatePsychology(title: String, type: String, content: String, time: Long, image: String,id: Long): Bo<PERSON>an {
        val cv = ContentValues()
        cv.put("title", title)
        cv.put("type", type)
        cv.put("content", content)
        cv.put("time", time)
        cv.put("image", image)
        return LitePal.update(PsychologyEntity::class.java, cv, id) > 0
    }

    /**
     * 根据条件查询你所不知道的心理学列表
     */
    fun getPsychologyList(title: String, startTime: Long, endTime: Long, type: String): List<PsychologyEntity> {
        var conditions = "title like ?"
        if (startTime > 0) {
            conditions = "$conditions and time > $startTime"
        }
        if (endTime > 0) {
            //这里的结束时间需要再加上24小时，否择查询到的数据就是前一天的数据
            conditions = "$conditions and time < ${endTime + 24 * 60 * 60 * 1000}"
        }
        if (type.isNotEmpty()) {
            conditions = "$conditions and type = '$type'"
        }
        return LitePal.where(conditions, "%$title%").order("time desc").find(PsychologyEntity::class.java,true)
    }

    /**
     * 查询你所不知道的心理学列表
     */
//    fun getPsychologyData(): List<PsychologyEntity> = LitePal.findAll(PsychologyEntity::class.java)

    /**
     * 根据类型查询你所不知道的心理学列表
     */
    fun getPsychologyData(type: Int): List<PsychologyEntity> {
        return when (type) {
            1 -> LitePal.where("type = ?", "亲子关系").order("time desc").find(PsychologyEntity::class.java)
            2 -> LitePal.where("type = ?", "情绪知识").order("time desc").find(PsychologyEntity::class.java)
            3 -> LitePal.where("type = ?", "压力调节").order("time desc").find(PsychologyEntity::class.java)
            4 -> LitePal.where("type = ?", "社交关系").order("time desc").find(PsychologyEntity::class.java)
            else -> LitePal.order("time desc").find(PsychologyEntity::class.java)
        }
    }

    /**
     * 根据类型查询你所不知道的心理学列表
     */
    fun getPsychologyData(type: String): List<PsychologyEntity> {
        return LitePal.where("type = ?", type).find(PsychologyEntity::class.java)
    }

    /**
     * 根据id获取你所不知道的心理学
     */
    fun getPsychologyById(id: Long): PsychologyEntity? = LitePal.find(PsychologyEntity::class.java, id)

    /**
     * 删除你所不知道的心理学
     */
    fun deletePsychology(id: Long): Boolean {
        return LitePal.delete(PsychologyEntity::class.java, id) > 0
    }
}