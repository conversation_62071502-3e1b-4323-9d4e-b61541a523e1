package com.ygxj.self.data.repository

import android.os.Build
import androidx.annotation.RequiresApi
import com.ygxj.self.AppApplication
import java.io.File

/**
 * 项目中用到的视频/图片/音乐等资源文件的管理
 */
object ResourceRepository {

    /**
     * 检查资源是否在设备上
     * @param path 资源在设备上的路径,与assets下的压缩包对应,
     * 例如,压缩包中ygxj_music_treatment/video/意象放松训练.mp4,
     * 对应设备上的路径就是Android/data/packageName/files/ygxj_music_treatment/video/意象放松训练.mp4
     *
     * @return true:已在设备上,false:未在设备上
     */
    @RequiresApi(Build.VERSION_CODES.FROYO)
    fun checkResourceIsExistAtSDCard(path: String): Boolean {
        val file = File("${AppApplication.appContext.getExternalFilesDir("self")}/${path}")
        return file.exists()
    }

//    /**
//     * 获取视频的完整路径
//     */
//    fun getVideoFilePath(video: Video): String {
//        return "${BaseApp.context.getExternalFilesDir("ygxj_music_treatment")}/${video.path}"
//    }
//
//    /**
//     * 获取音频的完整路径
//     */
//    fun getMusicFilePath(music: Music): String {
//        return "${BaseApp.context.getExternalFilesDir("ygxj_music_treatment")}/${music.path}"
//    }
//
//    /**
//     * 根据选中的音乐类别,加载对应的音乐类表
//     * @param musicType 音乐分类,见[com.ygxj.data.model.MusicType]
//     */
//    fun loadMusicListByMusicType(musicType: Int): List<Music> {
//        return getMusics().filter { it.type == musicType }
//    }
//
//    /**
//     * 获取音乐分类对应的文字名称
//     * @param musicType 音乐分类,见[com.ygxj.data.model.MusicType]
//     */
//    fun getMusicTypeNameByType(musicType: Int): String {
//        val typeText = when (musicType) {
//            MusicType.PROFESSIONAL -> "专业音乐"
//            else -> ""
//        }
//        return typeText
//    }
//
//    /**
//     * 获取视频资源
//     */
//    fun getVideos(): ArrayList<Video> {
//        return arrayListOf(
//            Video(name = "意象放松训练.mp4", path = "video/意象放松训练.mp4"),
//            Video(name = "腹式呼吸放松训练.mp4", path = "video/腹式呼吸放松训练.mp4"),
//            Video(name = "渐进式放松训练.mp4", path = "video/渐进式放松训练.mp4")
//        )
//    }
//
//    /**
//     * 获取音乐资源
//     */
//    fun getMusics(): ArrayList<Music> {
//        return arrayListOf(
//            // region 外国古典
//            Music("外国古典01.mp3", "music/1/外国古典/外国古典01.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典02.mp3", "music/1/外国古典/外国古典02.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典03.mp3", "music/1/外国古典/外国古典03.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典04.mp3", "music/1/外国古典/外国古典04.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典05.mp3", "music/1/外国古典/外国古典05.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典06.mp3", "music/1/外国古典/外国古典06.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典07.mp3", "music/1/外国古典/外国古典07.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典08.mp3", "music/1/外国古典/外国古典08.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典09.mp3", "music/1/外国古典/外国古典09.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典10.mp3", "music/1/外国古典/外国古典10.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典11.mp3", "music/1/外国古典/外国古典11.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典12.mp3", "music/1/外国古典/外国古典12.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典13.mp3", "music/1/外国古典/外国古典13.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典14.mp3", "music/1/外国古典/外国古典14.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典15.mp3", "music/1/外国古典/外国古典15.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典16.mp3", "music/1/外国古典/外国古典16.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典17.mp3", "music/1/外国古典/外国古典17.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典18.mp3", "music/1/外国古典/外国古典18.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典19.mp3", "music/1/外国古典/外国古典19.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            Music("外国古典20.mp3", "music/1/外国古典/外国古典20.mp3", MusicType.PROFESSIONAL_FOREIGN_CLASSICAL),
//            //endregion
//
//            //region 外国民乐
//            Music("外国民乐01.mp3", "music/1/外国民乐/外国民乐01.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐02.mp3", "music/1/外国民乐/外国民乐02.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐03.mp3", "music/1/外国民乐/外国民乐03.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐04.mp3", "music/1/外国民乐/外国民乐04.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐05.mp3", "music/1/外国民乐/外国民乐05.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐06.mp3", "music/1/外国民乐/外国民乐06.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐07.mp3", "music/1/外国民乐/外国民乐07.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐08.mp3", "music/1/外国民乐/外国民乐08.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐09.mp3", "music/1/外国民乐/外国民乐09.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐10.mp3", "music/1/外国民乐/外国民乐10.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐11.mp3", "music/1/外国民乐/外国民乐11.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐12.mp3", "music/1/外国民乐/外国民乐12.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐13.mp3", "music/1/外国民乐/外国民乐13.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐14.mp3", "music/1/外国民乐/外国民乐14.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐15.mp3", "music/1/外国民乐/外国民乐15.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐16.mp3", "music/1/外国民乐/外国民乐16.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐17.mp3", "music/1/外国民乐/外国民乐17.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐18.mp3", "music/1/外国民乐/外国民乐18.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐19.mp3", "music/1/外国民乐/外国民乐19.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("外国民乐20.mp3", "music/1/外国民乐/外国民乐20.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("穆特小提琴1独奏.mp3", "music/1/外国民乐/穆特小提琴1独奏.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("穆特小提琴4.mp3", "music/1/外国民乐/穆特小提琴4.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("allegro g-dur.mp3", "music/1/外国民乐/allegro g-dur.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("sonata for piano and violin in a minor, op.23 iii.mp3", "music/1/外国民乐/sonata for piano and violin in a minor, op.23 iii.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("sonata for piano and violin in f major, op.24 'spring' iv.mp3", "music/1/外国民乐/sonata for piano and violin in f major, op.24 'spring' iv.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            Music("sonata for piano and violin in g major, op.30 no.3 iii.mp3", "music/1/外国民乐/sonata for piano and violin in g major, op.30 no.3 iii.mp3", MusicType.PROFESSIONAL_FOREIGN_FOLK),
//            //endregion
//
//            //region 中国古典
//            Music("中国古典01.mp3", "music/1/中国古典/中国古典01.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典02.mp3", "music/1/中国古典/中国古典02.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典03.mp3", "music/1/中国古典/中国古典03.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典04.mp3", "music/1/中国古典/中国古典04.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典05.mp3", "music/1/中国古典/中国古典05.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典06.mp3", "music/1/中国古典/中国古典06.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典07.mp3", "music/1/中国古典/中国古典07.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典08.mp3", "music/1/中国古典/中国古典08.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典09.mp3", "music/1/中国古典/中国古典09.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典10.mp3", "music/1/中国古典/中国古典10.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典11.mp3", "music/1/中国古典/中国古典11.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典12.mp3", "music/1/中国古典/中国古典12.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典13.mp3", "music/1/中国古典/中国古典13.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典14.mp3", "music/1/中国古典/中国古典14.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典15.mp3", "music/1/中国古典/中国古典15.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典16.mp3", "music/1/中国古典/中国古典16.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典17.mp3", "music/1/中国古典/中国古典17.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典18.mp3", "music/1/中国古典/中国古典18.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典19.mp3", "music/1/中国古典/中国古典19.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            Music("中国古典20.mp3", "music/1/中国古典/中国古典20.mp3", MusicType.PROFESSIONAL_CHINESE_CLASSICAL),
//            //endregion
//
//            //region 中国民乐
//            Music("中国民乐01.mp3", "music/1/中国民乐/中国民乐01.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐02.mp3", "music/1/中国民乐/中国民乐02.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐03.mp3", "music/1/中国民乐/中国民乐03.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐04.mp3", "music/1/中国民乐/中国民乐04.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐05.mp3", "music/1/中国民乐/中国民乐05.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐06.mp3", "music/1/中国民乐/中国民乐06.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐07.mp3", "music/1/中国民乐/中国民乐07.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐08.mp3", "music/1/中国民乐/中国民乐08.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐09.mp3", "music/1/中国民乐/中国民乐09.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐10.mp3", "music/1/中国民乐/中国民乐10.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐11.mp3", "music/1/中国民乐/中国民乐11.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐12.mp3", "music/1/中国民乐/中国民乐12.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐13.mp3", "music/1/中国民乐/中国民乐13.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐14.mp3", "music/1/中国民乐/中国民乐14.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐15.mp3", "music/1/中国民乐/中国民乐15.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐16.mp3", "music/1/中国民乐/中国民乐16.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐17.mp3", "music/1/中国民乐/中国民乐17.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐18.mp3", "music/1/中国民乐/中国民乐18.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐19.mp3", "music/1/中国民乐/中国民乐19.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐20.mp3", "music/1/中国民乐/中国民乐20.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐 - 春天的故事.mp3", "music/1/中国民乐/中国民乐 - 春天的故事.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐 - 故乡的云.mp3", "music/1/中国民乐/中国民乐 - 故乡的云.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐 - 绿岛小夜曲.mp3", "music/1/中国民乐/中国民乐 - 绿岛小夜曲.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐 - 苗岭的早晨.mp3", "music/1/中国民乐/中国民乐 - 苗岭的早晨.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            Music("中国民乐 - 明月千里寄相思.mp3", "music/1/中国民乐/中国民乐 - 明月千里寄相思.mp3", MusicType.PROFESSIONAL_CHINESE_FOLK),
//            //endregion
//
//            //region α波音乐
//            Music("纯音乐 - Adagio.mp3", "music/2/纯音乐 - Adagio.mp3", MusicType.ALPHA),
//            Music("纯音乐 - Allegro con spirito.mp3", "music/2/纯音乐 - Allegro con spirito.mp3", MusicType.ALPHA),
//            Music("纯音乐 - Tempo primo.mp3", "music/2/纯音乐 - Tempo primo.mp3", MusicType.ALPHA),
//            Music("苔克拉·芭达捷芙丝卡 - 少女的祈祷.mp3", "music/2/苔克拉·芭达捷芙丝卡 - 少女的祈祷.mp3", MusicType.ALPHA),
//            Music("Wolfgang Amadeus Mozart - 01_C大调41号交响曲朱彼特.mp3", "music/2/Wolfgang Amadeus Mozart - 01_C大调41号交响曲朱彼特.mp3", MusicType.ALPHA),
//            Music("Wolfgang Amadeus Mozart - 10_降E大调法国号.mp3", "music/2/Wolfgang Amadeus Mozart - 10_降E大调法国号.mp3", MusicType.ALPHA),
//            Music("α波音乐盒 - 天空の城ラピュタ~『天空の城ラ.mp3", "music/2/α波音乐盒 - 天空の城ラピュタ~『天空の城ラ.mp3", MusicType.ALPHA),
//            //endregion
//
//            //region 纯音乐
//            Music("Bandari - I Swear.mp3", "music/3/Bandari - I Swear.mp3", MusicType.PURE),
//            Music("Bandari - Mystica（秘密）.mp3", "music/3/Bandari - Mystica（秘密）.mp3", MusicType.PURE),
//            Music("Bandari - Neptune.mp3", "music/3/Bandari - Neptune.mp3", MusicType.PURE),
//            Music("Bandari - Orinoco Dreams（奥里诺科之梦）.mp3", "music/3/Bandari - Orinoco Dreams（奥里诺科之梦）.mp3", MusicType.PURE),
//            Music("Bandari - Snow（静静的雪）.mp3", "music/3/Bandari - Snow（静静的雪）.mp3", MusicType.PURE),
//            Music("Bandari - Snowdreams（雪的梦幻）.mp3", "music/3/Bandari - Snowdreams（雪的梦幻）.mp3", MusicType.PURE)
//            //endregion
//        )
//    }
//
//    /**
//     * 获取音乐封面
//     */
//    fun getMusicCover(): ArrayList<Int> {
//        return arrayListOf(
//            R.mipmap.cover_01, R.mipmap.cover_02, R.mipmap.cover_03, R.mipmap.cover_04, R.mipmap.cover_05,
//            R.mipmap.cover_06, R.mipmap.cover_07, R.mipmap.cover_08, R.mipmap.cover_09, R.mipmap.cover_10,
//            R.mipmap.cover_11, R.mipmap.cover_12, R.mipmap.cover_13, R.mipmap.cover_14, R.mipmap.cover_15,
//            R.mipmap.cover_16, R.mipmap.cover_17, R.mipmap.cover_18, R.mipmap.cover_19, R.mipmap.cover_20,
//            R.mipmap.cover_21, R.mipmap.cover_22, R.mipmap.cover_23, R.mipmap.cover_24, R.mipmap.cover_25,
//            R.mipmap.cover_26, R.mipmap.cover_27, R.mipmap.cover_28, R.mipmap.cover_29, R.mipmap.cover_30
//        )
//    }

}