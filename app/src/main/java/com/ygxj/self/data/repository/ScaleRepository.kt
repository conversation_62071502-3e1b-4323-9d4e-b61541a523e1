package com.ygxj.self.data.repository

import android.content.ContentValues
import com.blankj.utilcode.util.ResourceUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ygxj.self.data.entity.ScaleDetailEntity
import com.ygxj.self.data.entity.ScaleEntity

import com.ygxj.self.data.entity.ScaleResultEntity
import org.litepal.LitePal

object ScaleRepository {

    /**
     * 添加默认数据
     */
    fun addDefaultScaleData() {
        val type = object : TypeToken<List<ScaleEntity>>() {}.type
        val scales: List<ScaleEntity> = Gson().fromJson(ResourceUtils.readAssets2String("default/scale.json"), type)
        saveScale(scales)
    }

    fun addDefaultScaleTopicData() {
        val type = object : TypeToken<List<ScaleDetailEntity>>() {}.type
        val topics: List<ScaleDetailEntity> = Gson().fromJson(ResourceUtils.readAssets2String("default/scaleTopic.json"), type)
      saveScaleTopic(topics)
    }

    /**
     * 判断量表是否为空
     */
    fun isEmptyScale(): Boolean = LitePal.findAll(ScaleEntity::class.java).isNullOrEmpty()

    /**
     * 保存所有量表
     */
    fun saveScale(topics: List<ScaleEntity>) = LitePal.saveAll(topics)

    /**
     * 根据条件查询量表列表
     */
    fun getScaleList(name: String,industry:Int): List<ScaleEntity> {
        return LitePal.where("name like ? and industry like ?", "%$name%", "%$industry%").find(
            ScaleEntity::class.java)
        //return LitePal.where("name like ?", "%$name%").find(ScaleEntity::class.java)
    }

    /**
     * 更新量表
     */
    fun updateScale(scaleEntity: ScaleEntity): Boolean {
        val cv = ContentValues()
        cv.put("desc", scaleEntity.desc)
        cv.put("guide", scaleEntity.guide)
        cv.put("resultStatus", scaleEntity.resultStatus == 1)
        cv.put("scaleStatus", scaleEntity.scaleStatus == 1)
        return LitePal.update(ScaleEntity::class.java, cv, scaleEntity.id) > 0
    }

    /**
     * 根据开放量表条件查询量表
     * industry是所属行业，根据行业的不同,查询不同的量表
     */
    fun getScaleData(industry:Int): List<ScaleEntity> {
        return LitePal.where("scaleStatus = 1 and industry like ?", "%$industry%").find(ScaleEntity::class.java)
        //return LitePal.where("scaleStatus = 1").find(ScaleEntity::class.java)
    }

    /**
     * 判断量表的题目是否为空
     */
    fun isEmptyScaleTopic(): Boolean = LitePal.findAll(ScaleDetailEntity::class.java).isNullOrEmpty()

    /**
     * 保存量表的题目
     */
    fun saveScaleTopic(topics: List<ScaleDetailEntity>) = LitePal.saveAll(topics)

    /**
     * 查询量表的题目的列表
     */
    fun getScaleTopicData(name: String): List<ScaleDetailEntity> = LitePal.where("type = ?", name).find(
        ScaleDetailEntity::class.java)

    /**
     * 根据名称查询量表信息
     */
    fun getScaleByName(name: String): ScaleEntity {
        return LitePal.where("name = ?", name).findFirst(ScaleEntity::class.java)
    }

    /**
     * 保存量表测试结果
     */
    fun saveScaleResult(movie: ScaleResultEntity): Boolean = movie.save()

    /**
     * 根据条件查询量表测试结果
     */
    fun getScaleResultList(name: String, startTime: Long, endTime: Long): List<ScaleResultEntity> {
        var conditions = "name like ?"
        if (startTime > 0) {
            conditions = "$conditions and time > $startTime"
        }
        if (endTime > 0) {
            conditions = "$conditions and time < $endTime"
        }
        return LitePal.where(conditions, "%$name%").order("time desc").find(ScaleResultEntity::class.java)
    }

    /**
     * 删除量表测试结果
     */
    fun deleteScaleResult(id: Long): Boolean {
        return LitePal.delete(ScaleResultEntity::class.java, id) > 0
    }

    /**
     * 根据id获取量表测试结果
     */
    fun getScaleResultById(id: Long): ScaleResultEntity = LitePal.find(ScaleResultEntity::class.java, id)
}