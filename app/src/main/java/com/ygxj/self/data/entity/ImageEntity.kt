package com.ygxj.self.data.entity

import org.litepal.annotation.Column
import org.litepal.crud.LitePalSupport
import java.io.Serializable

data class ImageEntity(
    var type: Int = 1, //1表示图片 2表示视频
    var path: String = "",
    var centerIntroductionEntity: CenterIntroductionEntity = CenterIntroductionEntity(),
    var titbitEntity: TitbitEntity = TitbitEntity()
) : LitePalSupport(), Serializable {
    @Column(unique = true)
    var id = 0L
}