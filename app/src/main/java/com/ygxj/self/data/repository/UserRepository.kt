package com.ygxj.self.data.repository

import android.content.ContentValues
import com.ygxj.self.data.entity.UserEntity
import org.litepal.LitePal

object UserRepository {

    /**
     * 查询所有用户
     */
    fun getAllUser(): List<UserEntity> = LitePal.findAll(UserEntity::class.java)

    /**
     * 根据账号查询用户
     */
    fun getUserByAccount(account: String): UserEntity? =
        LitePal.where("account = ?", account).findFirst(UserEntity::class.java)

    /**
     * 保存用户
     */
    fun saveUserData(user: UserEntity): Boolean = user.save()

    /**
     * 修改用户
     */
    fun updateUserData(user: UserEntity?) {
        user?.save()
    }
}