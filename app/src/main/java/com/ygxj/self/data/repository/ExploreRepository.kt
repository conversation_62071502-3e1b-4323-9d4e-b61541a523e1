package com.ygxj.self.data.repository

import android.content.ContentValues
import com.ygxj.self.data.entity.ExploreEntity
import org.litepal.LitePal

//心理探索
object ExploreRepository {

    /**
     * 判断是否为空
     */
    fun isEmptyExplore(): Boolean = LitePal.findAll(ExploreEntity::class.java).isNullOrEmpty()

    /**
     * 保存
     */
    fun saveExplore(entitys: List<ExploreEntity>) = LitePal.saveAll(entitys)

    /**
     * 保存
     */
    fun saveExplore(entity: ExploreEntity) = entity.save()

    /**
     * 修改
     */
    fun updateExplore(id: Long,name: String, info: String, content: String, path: String,type:Int): Boolean {
        val cv = ContentValues()
        cv.put("name", name)
        cv.put("info", info)
        cv.put("content", content)
        cv.put("path", path)
        cv.put("type", type)
        return LitePal.update(ExploreEntity::class.java, cv, id) > 0
    }

    /**
     * 根据条件查询
     */
    fun getExploreList(name: String): List<ExploreEntity> {
        val conditions = "name like ?"
        return LitePal.where(conditions, "%$name%").order("id desc").find(ExploreEntity::class.java)
    }

    /**
     * 删除
     */
    fun deleteExplore(id: Long): Boolean {
        return LitePal.delete(ExploreEntity::class.java, id) > 0
    }

    /**
     * 查询列表
     */
    fun getExploreData(): List<ExploreEntity> = LitePal.order("id desc").find(ExploreEntity::class.java)

    /**
     * 根据id获取
     */
    fun getExploreById(id: Long): ExploreEntity = LitePal.find(ExploreEntity::class.java, id)
}