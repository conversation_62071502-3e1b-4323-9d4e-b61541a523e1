package com.ygxj.self.data.repository

import android.content.ContentValues
import com.blankj.utilcode.util.ResourceUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ygxj.self.data.entity.ServiceRoomEntity
import com.ygxj.self.data.entity.TitbitEntity

import org.litepal.LitePal
import kotlin.collections.forEach

//心理服务室
object ServiceRoomRepository {
    /**
     * 保存心理服务室
     */
    fun addDefaultData() {
        val type = object : TypeToken<List<ServiceRoomEntity>>() {}.type
        val rooms: List<ServiceRoomEntity> =
            Gson().fromJson(ResourceUtils.readAssets2String("default/room.json"), type)
        rooms.forEach { room ->
            room.images.forEach { image ->
                image.save()
            }
            room.save()
        }
    }

    /**
     * 保存心理服务室
     */
    fun saveServiceRoom(room: ServiceRoomEntity) = room.save()

    /**
     * 修改心理服务室
     */
    fun updateServiceRoom(
        id: Long,
        name: String,
        info: String,
        path: String,
        imgIds: String
    ): Boolean {
        val cv = ContentValues()
        cv.put("name", name)
        cv.put("info", info)
        cv.put("path", path)
        cv.put("imgIds", imgIds)
        return LitePal.update(ServiceRoomEntity::class.java, cv, id) > 0
    }

    /**
     * 根据条件查询心理服务室
     */
    fun getServiceRoomList(name: String): List<ServiceRoomEntity> {
        val conditions = "name like ?"
        return LitePal.where(conditions, "%$name%").order("id desc")
            .find(ServiceRoomEntity::class.java)
    }

    /**
     * 删除心理服务室
     */
    fun deleteServiceRoom(id: Long): Boolean {
        return LitePal.delete(ServiceRoomEntity::class.java, id) > 0
    }

    /**
     * 查询心理服务室列表
     */
    fun getServiceRoomData(): List<ServiceRoomEntity> = LitePal.order("id desc").find(
        ServiceRoomEntity::class.java
    )

    /**
     * 根据id获取心理服务室
     */
    fun getServiceRoomById(id: Long): ServiceRoomEntity =
        LitePal.find(ServiceRoomEntity::class.java, id)
}