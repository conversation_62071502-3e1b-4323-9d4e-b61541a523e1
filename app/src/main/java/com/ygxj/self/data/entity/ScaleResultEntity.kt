package com.ygxj.self.data.entity

import org.litepal.annotation.Column
import org.litepal.crud.LitePalSupport
import java.io.Serializable

class ScaleResultEntity(
    var name: String= "",//量表名
    var answer: String= "",//用户选择结果
    var opinion: String= "",//测试结果意见
    var factor: String= "",//因素
    var standardScore: String= "",//标准分
    var averageScore: String= "",//均分
    var deviation: String= "",//标准差
    var suggestion: String= "",//辅导建议
    var diagnosis: String= "",//参考诊断
//    var warning: Int,//警告
    var time: Long= 0L,
    var date: String= "",//测试日期
    @Column(ignore = true)
    var selected: Boolean = false
): LitePalSupport(),Serializable {
    var id = 0L
}