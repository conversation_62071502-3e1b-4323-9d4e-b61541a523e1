package com.ygxj.self.data.repository

import android.content.ContentValues
import com.ygxj.self.data.entity.PsychologistEntity
import com.ygxj.self.data.entity.TimeEntity
import org.litepal.LitePal

object StyleRepository {

    /**
     * 判断心理咨询师是否为空
     */
    fun isEmptyPsychologist(): Boolean =
        LitePal.findAll(PsychologistEntity::class.java).isNullOrEmpty()

    /**
     * 保存心理咨询师
     */
    fun savePsychologists(psychologists: List<PsychologistEntity>) = LitePal.saveAll(psychologists)

    /**
     * 保存心理咨询师风采
     */
    fun savePsychologist(titbit: PsychologistEntity): Boolean = titbit.save()

    /** 修改心理咨询师风采 */
    fun updatePsychologist(
        name: String, gender: String, level: String, good: String, profile: String, path: String?,
        time: Long, id: Long
    ): <PERSON><PERSON><PERSON> {
        val cv = ContentValues()
        cv.put("name", name)
        cv.put("gender", gender)
        cv.put("level", level)
        cv.put("good", good)
        cv.put("profile", profile)
        cv.put("image", path)
        cv.put("time", time)
        return LitePal.update(PsychologistEntity::class.java, cv, id) > 0
    }

    /**
     * 根据条件查询心理咨询师风采列表
     */
    fun getPsychologistList(
        name: String,
        startTime: Long,
        endTime: Long
    ): List<PsychologistEntity> {
        var conditions = "name like ?"
        if (startTime > 0) {
            conditions = "$conditions and time > $startTime"
        }
        if (endTime > 0) {
            conditions = "$conditions and time < $endTime"
        }
        return LitePal.where(conditions, "%$name%").order("sort asc")
            .find(PsychologistEntity::class.java)
    }

    /**
     * 根据id获取心理咨询师风采
     */
    fun getPsychologistById(id: Long): PsychologistEntity =
        LitePal.find(PsychologistEntity::class.java, id)

    /**
     * 删除心理咨询师风采
     */
    fun deletePsychologist(id: Long): Boolean {
        return LitePal.delete(PsychologistEntity::class.java, id) > 0
    }

    /**
     * 根据条件查询心理咨询师风采列表
     */
    fun getPsychologistData(): List<PsychologistEntity> =
        LitePal.order("sort asc").find(PsychologistEntity::class.java)

    /**
     * 根据咨询师id和周几获取心理咨询师值班时间段
     */
    fun getPsyWorkTimeByIdAndWeek(psyId: Long, week: Int): List<TimeEntity> =
        LitePal.where("psyId = ? AND week = ?", psyId.toString(), week.toString())
            .find(TimeEntity::class.java)
}