package com.ygxj.self.data.repository

import android.content.ContentValues
import android.content.Context
import com.ygxj.self.R
import com.ygxj.self.data.entity.CenterIntroductionEntity
import com.ygxj.self.data.entity.ImageEntity
import org.litepal.LitePal

object CenterRepository {

    /**
     * 添加默认数据
     */
    fun addDefaultData(context: Context) {
        val image = ImageEntity(1, "${context.getExternalFilesDir("self")}/other/center.png")
        image.save()
        val centerEntity = CenterIntroductionEntity(
            context.getString(R.string.app_name),
            context.getString(R.string.center_content),
            mutableListOf(image)
        )
        saveCenterIntroduction(centerEntity)
    }

    /**
     * 查询中心介绍
     */
    fun getCenterIntroduction(): CenterIntroductionEntity? = LitePal.findFirst(
        CenterIntroductionEntity::class.java,true
    )

    /**
     * 保存中心介绍
     */
    fun saveCenterIntroduction(introduction: CenterIntroductionEntity?) =
        introduction?.save()

    /**
     * 修改中心介绍
     */
    fun updateCenterIntroduction(
        title: String,
        content: String,
        imgIds: String?,
        id: Long
    ): Boolean {
        val cv = ContentValues()
        cv.put("title", title)
        cv.put("content", content)
        // cv.put("image", image)
        cv.put("imgIds", imgIds)
        return LitePal.update(CenterIntroductionEntity::class.java, cv, id) > 0
    }
}