package com.ygxj.self.data.entity

import androidx.databinding.BaseObservable
import com.drake.brv.item.ItemDrag
import com.ygxj.self.utils.DateUtil
import org.litepal.annotation.Column
import org.litepal.crud.LitePalSupport
import java.io.Serializable
import com.drake.brv.annotaion.ItemOrientation


class TitbitEntity(
    var title: String = "",
    var content: String = "",
    var sort: Int = 0,
    var time: Long = 0L,
    var images: ArrayList<ImageEntity> = arrayListOf(),
    @Column(ignore = true)//忽略字段
    var isSelected: Boolean = false,
    @Column(ignore = true)//忽略字段
    override var itemOrientationDrag: Int = ItemOrientation.VERTICAL
) : LitePalSupport(), Serializable, ItemDrag {
    var id = 0L

    fun getDate(): String {
        return DateUtil.timestampToDateTime(time)
    }
}