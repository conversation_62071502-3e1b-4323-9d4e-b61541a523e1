package com.ygxj.self.data.repository


import com.blankj.utilcode.util.ResourceUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ygxj.self.data.entity.TopicDetailEntity
import com.ygxj.self.data.entity.TopicEntity
import org.litepal.LitePal

object FunnyRepository {
    /**
     * 添加默认数据
     */
    fun addDefaultFunnyData() {
        val type = object : TypeToken<List<TopicEntity>>() {}.type
        val funny: List<TopicEntity> = Gson().fromJson(ResourceUtils.readAssets2String("default/funny.json"), type)
        saveFunny(funny)
    }

    fun addDefaultTopicData() {
        val type = object : TypeToken<List<TopicDetailEntity>>() {}.type
        val topics: List<TopicDetailEntity> = Gson().fromJson(ResourceUtils.readAssets2String("default/topic.json"), type)
        saveTopic(topics)
    }

    /**
     * 判断funny是否为空
     */
    fun isEmptyFunny(): Boolean = LitePal.findAll(TopicEntity::class.java).isNullOrEmpty()

    /**
     * 查询funny的
     */
    fun getFunnyList(type: Int): List<TopicEntity> = LitePal.where("type = ?", type.toString()).find(
        TopicEntity::class.java)

    /**
     * 保存funny
     */
    fun saveFunny(topics: List<TopicEntity>) = LitePal.saveAll(topics)

    /**
     * 按职业查询funny的
     */
    fun getFunnyListByPerson(type: Int, person: Int): List<TopicEntity> {
        val conditions = "type = ? and person like '%$person%'"
        return LitePal.where(conditions, type.toString()).find(TopicEntity::class.java)
    }

    /**
     * 判断topic是否为空
     */
    fun isEmptyTopic(): Boolean = LitePal.findAll(TopicDetailEntity::class.java).isNullOrEmpty()

    /**
     * 查询topic的
     */
    fun getTopicList(name: String): List<TopicDetailEntity> = LitePal.where("type = ?", name).find(
        TopicDetailEntity::class.java)

    /**
     * 保存topic
     */
    fun saveTopic(topic: List<TopicDetailEntity>) = LitePal.saveAll(topic)
}