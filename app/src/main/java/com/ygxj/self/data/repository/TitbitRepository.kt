package com.ygxj.self.data.repository

import android.content.ContentValues
import com.blankj.utilcode.util.ResourceUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ygxj.self.data.entity.TitbitEntity

import org.litepal.LitePal

object TitbitRepository {

    //中心活动花絮默认数据
    fun addDefaultData() {
        val type = object : TypeToken<List<TitbitEntity>>() {}.type
        val titbit: List<TitbitEntity> =
            Gson().fromJson(ResourceUtils.readAssets2String("default/titbit.json"), type)
        titbit.forEach { titbit ->
            titbit.images.forEach { image ->
                image.save()
            }
            titbit.save()
        }
    }

    /**
     * 保存中心活动花絮
     */
    fun saveTitbit(titbit: TitbitEntity?) = titbit?.save()

    fun saveTitbit(titbitList: List<TitbitEntity>) = LitePal.saveAll(titbitList)

    /**
     * 根据条件查询中心活动花絮列表
     */
    fun getTitbitCount(): Int {
        return LitePal.findAll(TitbitEntity::class.java).size
    }

    /**
     * 根据条件查询中心活动花絮列表
     */
    fun getCenterTitbitList(title: String, startTime: Long, endTime: Long): List<TitbitEntity> {
        var conditions = "title like ?"
        if (startTime > 0) {
            conditions = "$conditions and time > $startTime"
        }
        if (endTime > 0) {
            //这里的结束时间需要再加上24小时，否择查询到的数据就是前一天的数据
            conditions = "$conditions and time < ${endTime + 24 * 60 * 60 * 1000}"
        }
        return LitePal.where(conditions, "%$title%").order("sort asc")
            .find(TitbitEntity::class.java, true)
    }

    /**
     * 根据id获取中心活动花絮
     */
    fun getCenterTitbitById(id: Long): TitbitEntity = LitePal.find(TitbitEntity::class.java, id,true)

    /**
     * 删除中心活动花絮
     */
    fun deleteCenterTitbit(id: Long): Boolean {
        return LitePal.delete(TitbitEntity::class.java, id) > 0
    }

    /**
     * 查询中心活动花絮列表
     */
    fun getTitbitData(): List<TitbitEntity> =
        LitePal.order("sort asc").find(TitbitEntity::class.java)
}