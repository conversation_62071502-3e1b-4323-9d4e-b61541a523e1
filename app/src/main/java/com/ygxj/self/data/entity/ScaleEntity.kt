package com.ygxj.self.data.entity

import org.litepal.annotation.Column
import org.litepal.crud.LitePalSupport

class ScaleEntity(
    var name: String= "",//名称
    var desc: String= "",//简介
    var guide: String= "",//指导语
    var descCopy: String= "",//简介备份 用来恢复初始状态时候使用
    var guideCopy: String= "",//指导语备份 用来恢复初始状态时候使用
    var resultStatus: Int= 0,//结果开启状态,开启为1
    var scaleStatus: Int= 0,//量表开启状态,开启为1
    var industry: String= ""//所属行业中学 高中 成人
): LitePalSupport(){
    var id = 0L
}