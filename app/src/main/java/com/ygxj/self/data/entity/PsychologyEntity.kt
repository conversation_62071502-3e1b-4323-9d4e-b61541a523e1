package com.ygxj.self.data.entity

import com.ygxj.self.utils.DateUtil
import org.litepal.annotation.Column
import org.litepal.crud.LitePalSupport
import java.io.Serializable
class PsychologyEntity(
    var title: String = "",
    var type: String = "",
    var content: String = "",
    var time: Long = 0L,
    var image: String = "",
): LitePalSupport(),Serializable{
    var id = 0L
    fun getDate(): String {
        return DateUtil.timestampToDateTime(time)
    }
}