package com.ygxj.self.data.entity

import org.litepal.annotation.Column
import org.litepal.crud.LitePalSupport

class TopicDetailEntity(
    var type: String= "",
    var title: String= "",
    var index: Int = 0,
    var image: String= "",
    var answer1: String= "",
    var answer2: String= "",
    var answer3: String= "",
    var answer4: String= "",
    var answer5: String= "",
    var answer6: String= "",
    var answer7: String= "",
    var answer8: String= "",
    var answer9: String= "",
    var answer10: String= "",
    var jump1: Int = 0,
    var jump2: Int = 0,
    var jump3: Int = 0,
    var jump4: Int = 0,
    var jump5: Int = 0,
    var jump6: Int = 0,
    var jump7: Int = 0,
    var jump8: Int = 0,
    var jump9: Int = 0,
    var jump10: Int = 0
): LitePalSupport(){
    var id = 0L
}