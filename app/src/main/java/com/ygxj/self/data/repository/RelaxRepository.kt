package com.ygxj.self.data.repository


import com.ygxj.self.data.entity.RelaxEntity
import org.litepal.LitePal
import kotlin.jvm.java

object RelaxRepository {
    /**
     * 保存放松资源列表，包括电影 音乐 图片
     */
    fun saveRelaxList(relax: List<RelaxEntity>) = LitePal.saveAll(relax)

    fun saveRelax(relax: RelaxEntity?) = relax?.save()

    /**
     * 根据条件查询放松图片
     */
    fun getRelaxPictureList(name: String, startTime: Long, endTime: Long): List<RelaxEntity> {
        var conditions = "name like ? and type = 3"
        if (startTime > 0) {
            conditions = "$conditions and time > $startTime"
        }
        if (endTime > 0) {
            //这里的结束时间需要再加上24小时，否择查询到的数据就是前一天的数据
            conditions = "$conditions and time < ${endTime + 24 * 60 * 60 * 1000}"
        }
        return LitePal.where(conditions, "%$name%").order("id desc").find(RelaxEntity::class.java)
    }

    /**
     * 根据条件查询放松音乐
     */
    fun getRelaxMusicList(name: String, startTime: Long, endTime: Long): List<RelaxEntity> {
        var conditions = "name like ? and type = 2"
        if (startTime > 0) {
            conditions = "$conditions and time > $startTime"
        }
        if (endTime > 0) {
            //这里的结束时间需要再加上24小时，否择查询到的数据就是前一天的数据
            conditions = "$conditions and time < ${endTime + 24 * 60 * 60 * 1000}"
        }
        return LitePal.where(conditions, "%$name%").order("id desc").find(RelaxEntity::class.java)
    }

    /**
     * 根据条件查询放松电影
     */
    fun getRelaxMovieList(name: String, startTime: Long, endTime: Long): List<RelaxEntity> {
        var conditions = "name like ? and type = 1"
        if (startTime > 0) {
            conditions = "$conditions and time > $startTime"
        }
        if (endTime > 0) {
            //这里的结束时间需要再加上24小时，否择查询到的数据就是前一天的数据
            conditions = "$conditions and time < ${endTime + 24 * 60 * 60 * 1000}"
        }
        return LitePal.where(conditions, "%$name%").order("id desc").find(RelaxEntity::class.java)
    }

    /**
     * 删除放松资源
     */
    fun deleteRelax(id: Long): Boolean {
        return LitePal.delete(RelaxEntity::class.java, id) > 0
    }

    /**
     * 查询放松图片列表
     */
    fun getRelaxPictureData(): List<RelaxEntity> = LitePal.order("id desc").where("type = 3").find(RelaxEntity::class.java)

    /**
     * 查询心理音乐列表
     */
    fun getRelaxMusicData(): List<RelaxEntity> = LitePal.order("id desc").where("type = 2").find(RelaxEntity::class.java)

    /**
     * 查询心理电影列表
     */
    fun getRelaxMovieData(): List<RelaxEntity> = LitePal.order("id desc").where("type = 1").find(RelaxEntity::class.java)

}