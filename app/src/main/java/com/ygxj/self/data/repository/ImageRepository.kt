package com.ygxj.self.data.repository

import com.blankj.utilcode.util.ResourceUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ygxj.self.data.entity.ImageEntity
import org.litepal.LitePal
import org.litepal.LitePal.where


object ImageRepository {

    /**
     * 添加默认数据
     */
    fun addDefaultData() {
        val type = object : TypeToken<List<ImageEntity>>() {}.type
        val images: List<ImageEntity> =
            Gson().fromJson(ResourceUtils.readAssets2String("default/image.json"), type)
        saveImages(images)
    }
    /**
     * 判断图片和视频是否为空
     */
    fun isEmptyImage(): Boolean = LitePal.findAll(ImageEntity::class.java).isNullOrEmpty()

    /**
     * 保存图片或视频
     */
    fun saveImage(image: ImageEntity): Boolean = image.save()

    /**
     * 删除图片或视频
     */
    fun deleteImage(image: ImageEntity) = image.delete()

    fun saveImages(imageList: List<ImageEntity>) = LitePal.saveAll(imageList)

    /**
     * 根据id获取图片资源
     */
    fun getImageById(id: Long): ImageEntity = LitePal.find(ImageEntity::class.java, id)


    fun getImageByIds(ids: String?): List<ImageEntity> {
        if (ids.isNullOrBlank()) return emptyList()
        val whereClause = "id in ($ids)"
        return where(whereClause).find(ImageEntity::class.java) // 进行查询并保存结果
    }


}