package com.ygxj.self.data.repository

import android.content.ContentValues
import com.ygxj.self.data.entity.GasStationEntity
import org.litepal.LitePal

//心理加油站
object GasStationRepository {

    /**
     * 判断是否为空
     */
    fun isEmptyGasStation(): Boolean = LitePal.findAll(GasStationEntity::class.java).isNullOrEmpty()

    /**
     * 保存
     */
    fun saveGasStations(entitys: List<GasStationEntity>) = LitePal.saveAll(entitys)

    /**
     * 保存
     */
    fun saveGasStation(entity: GasStationEntity) = entity.save()

    /**
     * 修改
     */
    fun updateGasStation(id: Long,content: String, people: String): Boolean {
        val cv = ContentValues()
        cv.put("content", content)
        cv.put("people", people)
        return LitePal.update(GasStationEntity::class.java, cv, id) > 0
    }

    /**
     * 根据条件查询
     */
    fun getGasStationList(content: String): List<GasStationEntity> {
        val conditions = "content like ?"
        return LitePal.where(conditions, "%$content%").order("id desc").find(GasStationEntity::class.java)
    }

    /**
     * 删除
     */
    fun deleteGasStation(id: Long): Boolean {
        return LitePal.delete(GasStationEntity::class.java, id) > 0
    }

    /**
     * 查询列表
     */
    fun getGasStationData(): List<GasStationEntity> = LitePal.order("id desc").find(GasStationEntity::class.java)

    /**
     * 根据id获取
     */
    fun getGasStationById(id: Long): GasStationEntity = LitePal.find(GasStationEntity::class.java, id)
}