package com.ygxj.self.data.repository


import com.ygxj.self.data.entity.TrainEntity
import org.litepal.LitePal

//行为训练
object TrainRepository {

    /**
     * 判断是否为空
     */
    fun isEmptyTrain(): Boolean = LitePal.findAll(TrainEntity::class.java).isNullOrEmpty()

    /**
     * 保存
     */
    fun saveTrain(entitys: List<TrainEntity>) = LitePal.saveAll(entitys)

    /**
     * 保存
     */
    fun saveTrain(entity: TrainEntity) = entity.save()

    /**
     * 根据条件查询
     */
    fun getTrainList(name: String): List<TrainEntity> {
        val conditions = "name like ?"
        return LitePal.where(conditions, "%$name%").find(TrainEntity::class.java)
    }

    /**
     * 删除
     */
    fun deleteTrain(id: Long): Boolean {
        return LitePal.delete(TrainEntity::class.java, id) > 0
    }

    /**
     * 查询列表
     */
    fun getTrainData(): List<TrainEntity> = LitePal.findAll(TrainEntity::class.java)

    /**
     * 根据id获取
     */
    fun getTrainById(id: Long): TrainEntity = LitePal.find(TrainEntity::class.java, id)
}