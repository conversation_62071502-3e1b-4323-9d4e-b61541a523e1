package com.ygxj.self

import android.app.Application
import android.content.Context
import com.drake.brv.utils.BRV
import com.drake.statelayout.StateConfig
import com.hjq.bar.TitleBar
import com.hjq.toast.Toaster
import com.scwang.smart.refresh.footer.ClassicsFooter
import com.scwang.smart.refresh.header.MaterialHeader
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.tencent.mmkv.MMKV
import com.ygxj.self.other.ActivityManager
import com.ygxj.self.other.DebugLoggerTree
import com.ygxj.self.other.TitleBarStyle
import org.litepal.LitePal
import timber.log.Timber


class AppApplication : Application() {

    /**
     * 全局context
     */
    companion object {
        lateinit var appContext: Context
    }

    override fun onCreate() {
        super.onCreate()
        appContext = applicationContext
        initializeThirdPart()
    }

    /** 初始化第三方依赖库库 */
    private fun initializeThirdPart() {

        LitePal.initialize(this)

        Toaster.init(this)

        // Activity 栈管理初始化
        ActivityManager.getInstance().init(this)

        // 设置标题栏初始化器
        TitleBar.setDefaultStyle(TitleBarStyle())

        // MMKV 初始化
        MMKV.initialize(this)

        // 初始化日志打印
        if (BuildConfig.DEBUG) {
            Timber.plant(DebugLoggerTree())
        }

        // 全局缺省页配置 [https://github.com/liangjingkanji/StateLayout]
        StateConfig.apply {
            emptyLayout = R.layout.layout_empty
            loadingLayout = R.layout.layout_loading
            errorLayout = R.layout.layout_empty
        }

        // 初始化SmartRefreshLayout, 这是自动下拉刷新和上拉加载采用的第三方库  [https://github.com/scwang90/SmartRefreshLayout/tree/master] V2版本
        SmartRefreshLayout.setDefaultRefreshHeaderCreator { context, _ ->
            MaterialHeader(context)
        }
        SmartRefreshLayout.setDefaultRefreshFooterCreator { context, _ ->
            ClassicsFooter(context)
        }

        //BRV列表配置 详见 https://liangjingkanji.github.io/BRV/
        BRV.modelId = BR.m
    }

}