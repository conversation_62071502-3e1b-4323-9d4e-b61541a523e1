<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="com.ygxj.self.ui.main.MainActivity">

        <ImageView
            android:id="@+id/iv_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/animation_bg_main" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/rl_top"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/ivLogo"
                    android:layout_width="84dp"
                    android:layout_height="64dp"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="50dp"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/icon_main_logo"
                    app:layout_constraintBottom_toBottomOf="@+id/llTitle"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/llTitle" />

                <LinearLayout
                    android:id="@+id/llTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="30dp"
                    android:layout_marginBottom="10dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tvTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/pangwa"
                        android:textColor="@color/white"
                        android:textSize="27sp"
                        android:text="@string/app_name" />

                    <TextView
                        android:id="@+id/tvSubTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:fontFamily="@font/pangwa"
                        android:textColor="@color/white"
                        android:textSize="18sp"
                        android:text="湖北省广水市广安路实验小学" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/rl_top"
                android:layout_marginBottom="50dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:paddingVertical="5dp">

                    <LinearLayout
                        android:id="@+id/llMenu"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:orientation="horizontal">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="10dp"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:id="@+id/ivMenu1"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_marginBottom="20dp"
                                android:background="@drawable/btn_main_item1"
                                app:layout_constraintDimensionRatio="85:80"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:text="中心介绍"
                                android:textColor="@color/white"
                                android:textSize="12sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/ivMenu1" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="10dp"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:id="@+id/ivMenu2"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_marginBottom="20dp"
                                android:background="@drawable/btn_main_item2"
                                app:layout_constraintDimensionRatio="85:80"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:text="中心活动花絮"
                                android:textColor="@color/white"
                                android:textSize="12sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/ivMenu2" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="10dp"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:id="@+id/ivMenu3"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_marginBottom="20dp"
                                android:background="@drawable/btn_main_item4"
                                app:layout_constraintDimensionRatio="85:80"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:text="你不知道的心理学"
                                android:textColor="@color/white"
                                android:textSize="12sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/ivMenu3" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="10dp"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:id="@+id/ivMenu4"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_marginBottom="20dp"
                                android:background="@drawable/btn_main_item3"
                                app:layout_constraintDimensionRatio="85:80"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:text="心灵鸡汤"
                                android:textColor="@color/white"
                                android:textSize="12sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/ivMenu4" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </LinearLayout>

                    <ImageView
                        android:id="@+id/ivDragonfly"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="-20dp"
                        android:src="@drawable/animation_dragonfly"
                        android:layout_toEndOf="@+id/llMenu" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal"
                    android:paddingVertical="5dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/ivMenu6"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginBottom="20dp"
                            android:background="@drawable/btn_main_item6"
                            app:layout_constraintDimensionRatio="85:80"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="心理咨询师风采"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/ivMenu6" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/ivMenu5"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginBottom="20dp"
                            android:background="@drawable/btn_main_item5"
                            app:layout_constraintDimensionRatio="85:80"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="我是谁"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/ivMenu5" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/ivMenu7"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginBottom="20dp"
                            android:background="@drawable/btn_main_item7"
                            app:layout_constraintDimensionRatio="85:80"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="心理服务室"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/ivMenu7" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/ivMenu8"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginBottom="20dp"
                            android:background="@drawable/btn_main_item8"
                            app:layout_constraintDimensionRatio="85:80"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="心理行为训练"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/ivMenu8" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/ivMenu9"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginBottom="20dp"
                            android:background="@drawable/btn_main_item9"
                            app:layout_constraintDimensionRatio="85:80"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="心理探索"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/ivMenu9" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>

            </LinearLayout>

            <com.ygxj.self.other.PressAlphaImageView
                android:id="@+id/ivEnterManager"
                android:layout_width="47dp"
                android:layout_height="50dp"
                android:layout_alignParentBottom="true"
                android:layout_margin="5dp"
                android:background="@drawable/btn_enter_manager"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:textColor="@color/white"
                android:textSize="11sp" />
        </RelativeLayout>
    </RelativeLayout>

</layout>