<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="5dp"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintDimensionRatio="75:100" >

            <Button
                android:id="@+id/btn_add_image"
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:layout_margin="5dp"
                android:background="@drawable/btn_manage_edit"
                android:text="选择图片"
                android:textColor="@color/white"
                android:textSize="9sp" />

            <Button
                android:id="@+id/btn_add_video"
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:layout_margin="5dp"
                android:background="@drawable/btn_manage_edit"
                android:text="选择视频"
                android:textColor="@color/white"
                android:textSize="9sp" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>