<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        android:orientation="vertical"
        tools:context="com.ygxj.self.ui.soul.movie.MovieDetailActivity">

        <androidx.media3.ui.PlayerView
            android:id="@+id/playerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:keepScreenOn="true"
            app:auto_show="false"
            app:resize_mode="fit"
            app:show_next_button="false"
            app:show_previous_button="false" />

        <RelativeLayout
            android:id="@+id/rl_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible">

            <com.ygxj.self.other.PressAlphaImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:padding="15dp"
                android:src="@drawable/ic_back"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginHorizontal="50dp"
                android:text="标题"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </RelativeLayout>

        <androidx.core.widget.ContentLoadingProgressBar
            android:id="@+id/progressBar"
            style="@style/Base.Widget.AppCompat.ProgressBar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:max="100"
            android:progress="50"
            android:theme="@style/ContentLoadingProgress" />

    </RelativeLayout>

</layout>