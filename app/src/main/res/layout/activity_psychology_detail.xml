<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_login"
        tools:context=".ui.main.psychology.detail.PsychologyDetailNewActivity">

        <FrameLayout
            android:id="@+id/flTitleBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@id/vBookBg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/white"
                android:textSize="17sp"
                tools:ignore="HardcodedText"
                tools:text="标题" />

        </FrameLayout>

        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:src="@drawable/ic_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <FrameLayout
            android:id="@+id/vBookBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/bg_book"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.80"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.80">

            <com.ygxj.self.ui.psychology.book.PageFlipView
                android:id="@+id/pageFlipView"
                android:layout_marginHorizontal="30dp"
                android:layout_marginTop="7dp"
                android:layout_marginBottom="5dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </FrameLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnPreBook"
            android:layout_width="58dp"
            android:layout_height="23dp"
            android:text="上一本"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:layout_centerVertical="true"
            android:background="@drawable/bg_button_blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@id/vBookBg"
            app:layout_constraintTop_toBottomOf="@id/vBookBg"
            tools:ignore="ContentDescription" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnNextBook"
            android:layout_width="58dp"
            android:layout_height="23dp"
            android:text="下一本"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@drawable/bg_button_blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/vBookBg"
            app:layout_constraintTop_toBottomOf="@id/vBookBg"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>