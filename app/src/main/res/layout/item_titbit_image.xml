<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_marginBottom="10dp"
            android:id="@+id/iv_cover"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintDimensionRatio="1:1"
            android:scaleType="centerCrop"
            tools:src="@tools:sample/backgrounds/scenic" />

        <View
            android:id="@+id/v_mask"
            android:background="#80000000"
            app:layout_constraintStart_toStartOf="@+id/iv_cover"
            app:layout_constraintEnd_toEndOf="@+id/iv_cover"
            app:layout_constraintTop_toTopOf="@+id/iv_cover"
            app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
            android:layout_width="0dp"
            android:layout_height="0dp"/>

        <ImageView
            android:id="@+id/iv_play"
            android:src="@drawable/ic_play"
            app:layout_constraintStart_toStartOf="@+id/iv_cover"
            app:layout_constraintEnd_toEndOf="@+id/iv_cover"
            app:layout_constraintTop_toTopOf="@+id/iv_cover"
            app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
            android:layout_width="20dp"
            android:layout_height="20dp"/>

        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/iv_delete"
            android:src="@drawable/ic_delete"
            app:layout_constraintEnd_toEndOf="@+id/iv_cover"
            app:layout_constraintTop_toTopOf="@+id/iv_cover"
            android:layout_width="20dp"
            android:layout_height="20dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>