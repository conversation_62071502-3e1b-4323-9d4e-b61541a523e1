<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:banner="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_login"
        tools:context="com.ygxj.self.ui.center.CenterIntroActivity">

        <FrameLayout
            android:id="@+id/flTitleBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@id/llContent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvTitle"
                android:text="心理咨询中心"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/white"
                android:textSize="17sp"
                tools:ignore="HardcodedText" />

        </FrameLayout>

        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:src="@drawable/ic_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/ivDandelion"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:src="@drawable/bg_introduce"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <LinearLayout
            android:id="@+id/llContent"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/bg_center_introduction"
            android:orientation="horizontal"
            android:padding="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.8">

            <com.youth.banner.Banner
                android:id="@+id/banner"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="0.8"
                banner:banner_indicator_normal_color="@android:color/white"
                banner:banner_indicator_selected_color="@color/common_primary_color" />

            <ScrollView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="2"
                android:fadingEdgeLength="26dp"
                android:overScrollMode="never"
                android:requiresFadingEdge="vertical"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tvCenterContent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/lightBlack"
                        android:textSize="11sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="@string/center_content" />
                </LinearLayout>
            </ScrollView>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>