<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="m"
            type="com.ygxj.self.data.entity.RelaxEntity" />
    </data>

    <LinearLayout
        android:id="@+id/item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="10dp"
        android:paddingTop="3dp"
        android:paddingEnd="10dp"
        android:paddingBottom="3dp">

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tvMusic"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:singleLine="true"
            selected="@{m.checked}"
            android:textColor="@color/white"
            app:shape_textSelectedColor="@color/common_primary_color"
            android:textSize="11sp"
            tools:text="这是一个很寂寞的天" />

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tvDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="5dp"
            android:paddingEnd="5dp"
            selected="@{m.checked}"
            app:shape_textSelectedColor="@color/common_primary_color"
            android:textColor="@color/white"
            android:textSize="11sp"
            tools:text="02:20" />
    </LinearLayout>
</layout>