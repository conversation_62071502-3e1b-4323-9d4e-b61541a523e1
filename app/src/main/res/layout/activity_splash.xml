<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        tools:context="com.ygxj.self.ui.splash.SplashActivity">

        <LinearLayout
            android:id="@+id/ll_progress"
            android:layout_width="355dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="正在解压数据，请稍后..."
                android:textColor="@color/white"
                android:textSize="14sp" />

            <ProgressBar
                android:id="@+id/progress_bar"
                style="@android:style/Widget.ProgressBar.Horizontal"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:layout_marginTop="5dp"
                android:max="100"
                android:progress="50"
                android:progressBackgroundTint="@color/white"
                android:progressTint="#fcca35" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_launcher" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:textColor="@color/white"
                android:textSize="17sp"
                tools:text="@string/app_name" />
        </LinearLayout>
    </LinearLayout>
</layout>