<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_login"
        tools:context="com.ygxj.self.ui.titbit.TitbitActivity">

        <LinearLayout
            android:id="@+id/flTitleBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="中心活动花絮"
                android:textColor="@color/white"
                android:textSize="17sp"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tvDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="5dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                tools:ignore="HardcodedText"
                tools:text="2025-02-14" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvNum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="10sp"
            app:layout_constraintEnd_toEndOf="@+id/vp"
            app:layout_constraintTop_toBottomOf="@+id/flTitleBar"
            tools:text="【1/10】" />

        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:src="@drawable/ic_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="10dp"
            android:layout_marginVertical="5dp"
            app:layout_constraintBottom_toTopOf="@+id/rv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvNum"
            app:layout_constraintWidth_percent="0.7" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="0dp"
            android:layout_height="35dp"
            android:layout_marginBottom="30dp"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.8"
            tools:itemCount="15"
            tools:listitem="@layout/item_center_titbit" />

        <ImageView
            android:id="@+id/ivDandelion"
            android:layout_width="100dp"
            android:layout_height="90dp"
            android:src="@drawable/bg_highlights"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/ivLionGif"
            android:layout_width="60dp"
            android:layout_height="100dp"
            android:layout_marginEnd="2dp"
            android:layout_marginBottom="10dp"
            android:importantForAccessibility="no"
            android:src="@drawable/animation_shi_zi"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <RelativeLayout
            android:id="@+id/rlQiBao"
            android:layout_width="90dp"
            android:layout_height="50dp"
            android:background="@drawable/ic_qi_bao"
            app:layout_constraintBottom_toTopOf="@id/ivLionGif"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginHorizontal="5dp"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="10dp"
                android:text="轻轻一点，我来介绍给你听"
                android:textColor="@color/white"
                android:textSize="9sp" />
        </RelativeLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>