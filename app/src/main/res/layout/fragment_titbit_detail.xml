<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:banner="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>


    </data>

    <LinearLayout
        tools:background="@color/common_primary_color"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context="com.ygxj.self.ui.titbit.TitbitDetailFragment">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                tools:ignore="UseCompoundDrawables">

                <com.youth.banner.Banner
                    android:id="@+id/banner"
                    android:layout_width="match_parent"
                    android:layout_height="180dp"
                    banner:banner_indicator_normal_color="@android:color/white"
                    banner:banner_indicator_selected_color="@color/common_primary_color"/>

                <TextView
                    android:id="@+id/tvContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:lineSpacingExtra="2dp"
                    android:textColor="@color/lighterBlack"
                    android:textSize="10sp"
                    tools:text="哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈" />
            </LinearLayout>

        </ScrollView>

    </LinearLayout>
</layout>