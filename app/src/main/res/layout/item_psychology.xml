<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.self.data.entity.PsychologyEntity" />
    </data>

    <RelativeLayout
        android:id="@+id/item"
        android:layout_width="180dp"
        android:layout_height="match_parent">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#b9e3fb"
            app:cardCornerRadius="2dp"
            app:cardElevation="3dp"
            app:cardUseCompatPadding="true"
            app:cardPreventCornerOverlap="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="5dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/ivBook"
                    loadImage="@{m.image}"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginHorizontal="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_weight="1"
                    android:scaleType="centerCrop" />

                <TextView
                    android:id="@+id/tvPsychologyTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:layout_marginTop="5dp"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="@{m.title}"
                    android:textColor="#3b3b3b"
                    android:textSize="12sp"
                    tools:text="这是一个很寂寞的天" />

                <TextView
                    android:id="@+id/tvPsychologyContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="10dp"
                    android:ellipsize="end"
                    android:lines="2"
                    android:text="@{m.content}"
                    android:textColor="@color/lighterBlack"
                    android:textSize="9sp"
                    tools:text="这是一个很寂寞的天" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>


    </RelativeLayout>

</layout>