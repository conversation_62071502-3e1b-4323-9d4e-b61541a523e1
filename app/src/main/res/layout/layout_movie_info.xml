<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <com.hjq.shape.layout.ShapeRelativeLayout
        android:layout_width="200dp"
        android:layout_height="140dp"
        android:background="@color/lighterBlack"
        app:shape_radius="5dp"
        app:shape_solidColor="@color/white">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="25dp"
            android:layout_marginTop="5dp"
            android:layout_toStartOf="@+id/ivClose"
            android:gravity="center"
            android:text="标题"
            android:textSize="10sp" />

        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/ivClose"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="5dp"
            android:padding="6dp"
            android:src="@drawable/ic_tip_close" />

        <com.hjq.shape.layout.ShapeLinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/tvPlay"
            android:layout_below="@+id/tvTitle"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="2dp"
            android:orientation="vertical"
            app:shape_radius="5dp"
            app:shape_solidColor="#eeeeee">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:text="简介"
                android:textSize="10sp" />

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/tvContent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:layout_marginTop="2dp"
                    android:text="无"
                    android:textSize="8sp" />
            </ScrollView>

        </com.hjq.shape.layout.ShapeLinearLayout>

        <com.ygxj.self.other.PressAlphaTextView
            android:id="@+id/tvPlay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_marginVertical="5dp"
            android:layout_marginEnd="10dp"
            android:text="立即播放"
            android:textColor="#6faff8"
            android:textSize="8sp" />

    </com.hjq.shape.layout.ShapeRelativeLayout>
</layout>