<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>


    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="5dp"
        tools:context="com.ygxj.self.ui.back.center.CenterFragment">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="标题："
                        android:textColor="@color/lightBlack"
                        android:textSize="11sp" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etTitle"
                        android:layout_width="match_parent"
                        android:layout_height="25dp"
                        android:background="@drawable/bg_edit_blue"
                        android:paddingStart="5dp"
                        android:paddingEnd="5dp"
                        android:singleLine="true"
                        android:textColor="@color/lightBlack"
                        android:textSize="11sp" />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginTop="5dp"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="7"
                    tools:itemCount="5"
                    tools:listitem="@layout/item_center_image" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="150dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:paddingTop="5dp"
                        android:text="内容："
                        android:textColor="@color/lightBlack"
                        android:textSize="11sp" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etContent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/bg_edit_blue"
                        android:gravity="top"
                        android:lineSpacingMultiplier="1.4"
                        android:padding="5dp"
                        android:textColor="@color/lighterBlack"
                        android:textSize="11sp" />
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnSave"
                android:layout_width="65dp"
                android:layout_height="25dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/btn_manage_edit"
                android:text="保存"
                android:textColor="@color/white"
                android:textSize="9sp" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnPreview"
                android:layout_width="65dp"
                android:layout_height="25dp"
                android:layout_marginStart="10dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/btn_manage_edit"
                android:text="预览"
                android:textColor="@color/white"
                android:textSize="9sp" />
        </LinearLayout>

    </LinearLayout>

</layout>