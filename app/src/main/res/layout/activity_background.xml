<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_login"
        android:orientation="vertical"
        tools:context=".ui.back.BackgroundActivity">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/ivLogo"
                android:layout_width="78dp"
                android:layout_height="20dp"
                android:layout_marginStart="15dp"
                android:src="@drawable/ic_logo" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="15dp"
                android:layout_weight="1"
                android:fontFamily="@font/pangwa"
                android:textColor="@color/white"
                android:textSize="16sp"
                tools:text="@string/app_name" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:gravity="center"
                android:orientation="vertical">

                <ImageButton
                    android:id="@+id/btnBackToFront"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:background="@drawable/bg_back_menu"
                    android:padding="5dp"
                    android:src="@drawable/ic_back_arrow" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="返回前台"
                    android:textColor="@color/white"
                    android:textSize="8sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:gravity="center"
                android:orientation="vertical">

                <ImageButton
                    android:id="@+id/btnEditPwd"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:background="@drawable/bg_back_menu"
                    android:padding="8dp"
                    android:src="@drawable/ic_back_key" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="修改密码"
                    android:textColor="@color/white"
                    android:textSize="8sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llAlarmClock"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:gravity="center"
                android:orientation="vertical">

                <ImageButton
                    android:id="@+id/btnAlarmClock"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:background="@drawable/bg_back_menu"
                    android:padding="10dp"
                    android:src="@drawable/ic_alarm_clock" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="定时关机"
                    android:textColor="@color/white"
                    android:textSize="8sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llChangeInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:gravity="center"
                android:orientation="vertical">

                <ImageButton
                    android:id="@+id/btnChangeInfo"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:background="@drawable/bg_back_menu"
                    android:padding="10dp"
                    android:src="@drawable/ic_set_up" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="修改配置"
                    android:textColor="@color/white"
                    android:textSize="8sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:gravity="center"
                android:orientation="vertical">

                <ImageButton
                    android:id="@+id/btnBackToDesktop"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:background="@drawable/bg_back_menu"
                    android:padding="10dp"
                    android:src="@drawable/ic_back_exit" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="退出系统"
                    android:textColor="@color/white"
                    android:textSize="8sp" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <com.angcyo.tablayout.DslTabLayout
                android:id="@+id/tab_layout"
                android:layout_marginHorizontal="5dp"
                android:layout_width="90dp"
                android:layout_height="match_parent"
                app:tab_select_color="@color/white"
                app:tab_deselect_color="@color/white"
                app:tab_orientation="VERTICAL">

                <Button
                    android:id="@+id/btnCenterIntro"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="中心介绍"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/btnCenterTitbit"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="中心活动花絮"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/btnPsychology"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="你所不知道的心理学"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/btnPsychologist"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="心理咨询师风采"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/btnMovie"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="心理电影"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/btnMusic"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="心理音乐"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/btnPicture"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="放松图片"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/btnScale"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="量表管理"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/btnServiceRoom"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="心理服务室"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/btnBehaviorTraining"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="心理行为训练"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/btnGasStation"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="心理加油站"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

                <Button
                    android:id="@+id/btnExplore"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_back_item"
                    android:text="心理探索"
                    android:textColor="@color/white"
                    android:textSize="8sp" />

            </com.angcyo.tablayout.DslTabLayout>

            <FrameLayout
                android:id="@+id/flContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>

    </LinearLayout>

</layout>