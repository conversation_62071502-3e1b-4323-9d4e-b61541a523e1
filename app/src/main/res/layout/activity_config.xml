<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ContentDescription">

    <data>


    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.main.config.ConfigActivity">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/bg_login"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="364dp"
            android:layout_height="300dp"
            android:layout_centerInParent="true"
            android:background="@drawable/bg_login_main"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="配置"
                android:textColor="#00a8eb"
                android:textSize="20sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="28dp"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical|end"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    android:text="主标题："
                    android:textSize="14sp" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/tvTitle"
                    android:layout_width="220dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="40dp"
                    android:background="@drawable/bg_edit_blue"
                    android:hint="必填"
                    android:inputType="text"
                    android:paddingStart="5dp"
                    android:singleLine="true"
                    android:textColor="@color/lightBlack"
                    android:textColorHint="@color/lightestBlack"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="28dp"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical|end"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    android:text="副标题："
                    android:textSize="14sp" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/tvSubTitle"
                    android:layout_width="220dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="40dp"
                    android:background="@drawable/bg_edit_blue"
                    android:hint="选填"
                    android:inputType="text"
                    android:paddingStart="5dp"
                    android:singleLine="true"
                    android:textColor="@color/lightBlack"
                    android:textColorHint="@color/lightestBlack"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="28dp"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical|end"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    android:text="Logo："
                    android:textSize="14sp" />

                <RadioGroup
                    android:id="@+id/rgLogo"
                    android:layout_width="220dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="40dp"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/rbShow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="显示"
                        android:textSize="14sp" />

                    <RadioButton
                        android:id="@+id/rbNotShow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="不显示"
                        android:textSize="14sp" />
                </RadioGroup>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="28dp"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical|end"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    android:text="行业："
                    android:textSize="14sp" />

                <RadioGroup
                    android:id="@+id/rgIndustry"
                    android:layout_width="220dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="40dp"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/rbSchool1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="中小学"
                        android:textSize="14sp" />

                    <RadioButton
                        android:id="@+id/rbSchool2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="高校"
                        android:textSize="14sp" />

                    <RadioButton
                        android:id="@+id/rbSchool3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="成人"
                        android:textSize="14sp" />
                </RadioGroup>

            </LinearLayout>

            <Button
                android:id="@+id/btnSave"
                android:layout_width="80dp"
                android:layout_height="26dp"
                android:layout_margin="10dp"
                android:background="@drawable/btn_login_blue"
                android:text="确认"
                android:textColor="@color/white"
                android:textSize="12sp" />
        </LinearLayout>

    </RelativeLayout>
</layout>