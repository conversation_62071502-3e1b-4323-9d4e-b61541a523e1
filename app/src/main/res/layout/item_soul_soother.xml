<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="m"
            type="com.ygxj.self.data.entity.RelaxEntity" />
    </data>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="5dp"
        tools:background="@color/common_primary_color">

        <Space
            android:id="@+id/topSpace"
            android:layout_width="match_parent"
            android:layout_height="20dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Space
                android:id="@+id/startSpace"
                android:layout_width="20dp"
                android:layout_height="match_parent" />

            <RelativeLayout
                android:layout_width="100dp"
                android:layout_height="75dp">

                <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
                    android:id="@+id/ivMovie"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    app:qmui_border_color="#9ad3fb"
                    app:qmui_border_width="2dp"
                    app:qmui_corner_radius="2dp"
                    tools:src="@drawable/placeholder" />

                <com.hjq.shape.view.ShapeTextView
                    android:id="@+id/tvMovie"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:paddingHorizontal="5dp"
                    android:paddingVertical="3dp"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textSize="8sp"
                    app:shape_radiusInBottomLeft="2dp"
                    app:shape_radiusInBottomRight="2dp"
                    app:shape_solidColor="#50000000"
                    tools:text="这是一个很寂寞的天" />

            </RelativeLayout>

        </LinearLayout>


    </LinearLayout>
</layout>