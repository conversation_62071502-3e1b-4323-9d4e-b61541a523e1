<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_login"
        tools:context="com.ygxj.self.ui.psychology.PsychologyActivity">

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/ivDandelion"
            android:layout_width="220dp"
            android:layout_height="220dp"
            android:scaleType="fitXY"
            android:src="@drawable/bg_dandelion"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <FrameLayout
            android:id="@+id/flTitleBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@id/llContent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="你所不知道的心理学"
                android:textColor="@color/white"
                android:textSize="17sp"
                tools:ignore="HardcodedText" />

        </FrameLayout>

        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:src="@drawable/ic_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <LinearLayout
            android:id="@+id/llContent"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="20dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.65"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.55"
            app:layout_constraintWidth_percent="0.8">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="10dp"
                android:gravity="center"
                android:orientation="horizontal">

                <RelativeLayout
                    android:id="@+id/ivPsyItem1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ivPsyItem2"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageButton
                        android:id="@+id/btnPsyItem1"
                        android:layout_width="105dp"
                        android:layout_height="35dp"
                        android:background="@drawable/bg_psy_item1" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="45dp"
                        android:text="亲子关系"
                        android:textColor="@color/white"
                        android:textSize="9sp" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/ivPsyItem2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="@id/ivPsyItem1"
                    app:layout_constraintEnd_toStartOf="@id/ivPsyItem3"
                    app:layout_constraintStart_toEndOf="@id/ivPsyItem1"
                    app:layout_constraintTop_toTopOf="@id/ivPsyItem1">

                    <ImageButton
                        android:id="@+id/btnPsyItem2"
                        android:layout_width="105dp"
                        android:layout_height="35dp"
                        android:background="@drawable/bg_psy_item2" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="45dp"
                        android:text="情绪知识"
                        android:textColor="@color/white"
                        android:textSize="9sp" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/ivPsyItem3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="@id/ivPsyItem1"
                    app:layout_constraintEnd_toStartOf="@id/ivPsyItem4"
                    app:layout_constraintStart_toEndOf="@id/ivPsyItem2"
                    app:layout_constraintTop_toTopOf="@id/ivPsyItem1">

                    <ImageButton
                        android:id="@+id/btnPsyItem3"
                        android:layout_width="105dp"
                        android:layout_height="35dp"
                        android:background="@drawable/bg_psy_item3" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="45dp"
                        android:text="压力调节"
                        android:textColor="@color/white"
                        android:textSize="9sp" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/ivPsyItem4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="@id/ivPsyItem1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ivPsyItem3"
                    app:layout_constraintTop_toTopOf="@id/ivPsyItem1">

                    <ImageButton
                        android:id="@+id/btnPsyItem4"
                        android:layout_width="105dp"
                        android:layout_height="35dp"
                        android:background="@drawable/bg_psy_item4" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="45dp"
                        android:text="社交关系"
                        android:textColor="@color/white"
                        android:textSize="9sp" />
                </RelativeLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="5"
                tools:listitem="@layout/item_psychology" />
        </LinearLayout>

        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/ivPre"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginEnd="10dp"
            android:src="@drawable/ic_pre"
            app:layout_constraintBottom_toBottomOf="@+id/llContent"
            app:layout_constraintEnd_toStartOf="@+id/llContent"
            app:layout_constraintTop_toTopOf="@+id/llContent" />

        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/ivNext"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginStart="10dp"
            android:src="@drawable/ic_next"
            app:layout_constraintBottom_toBottomOf="@+id/llContent"
            app:layout_constraintStart_toEndOf="@+id/llContent"
            app:layout_constraintTop_toTopOf="@+id/llContent" />

        <TextView
            android:id="@+id/tvNum"
            android:layout_width="100dp"
            android:layout_height="30dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/bg_psy_num"
            android:gravity="center"
            android:text='共0条'
            android:textColor="@color/white"
            android:textSize="10sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/llContent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>