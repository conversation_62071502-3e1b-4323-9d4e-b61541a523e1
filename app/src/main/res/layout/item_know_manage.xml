<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.self.data.entity.PsychologyEntity" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="22dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/colorBlueLine" />

            <TextView
                android:id="@+id/tvNo"
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/lighterBlack"
                android:textSize="8sp"
                tools:text="1" />

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/colorBlueLine" />

            <TextView
                android:id="@+id/tvTitle"
                android:text="@{m.title}"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:paddingHorizontal="5dp"
                android:singleLine="true"
                android:textColor="@color/lighterBlack"
                android:textSize="8sp"
                tools:text="这是一个很寂寞的天" />

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/colorBlueLine" />

            <TextView
                android:id="@+id/tvType"
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:text="@{m.type}"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/lighterBlack"
                android:textSize="8sp"
                tools:text="这是一个很寂寞的天" />

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/colorBlueLine" />

            <TextView
                android:id="@+id/tvTime"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@{m.getDate()}"
                android:textColor="@color/lighterBlack"
                android:textSize="8sp"
                tools:text="2022-10-28 12:00:00" />

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/colorBlueLine" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <Button
                    android:id="@+id/btnPreview"
                    android:layout_width="30dp"
                    android:layout_height="15dp"
                    android:layout_marginEnd="3dp"
                    android:background="@drawable/btn_manage_edit"
                    android:text="预览"
                    android:textColor="@color/white"
                    android:textSize="6sp" />

                <Button
                    android:id="@+id/btnEdit"
                    android:layout_width="30dp"
                    android:layout_height="15dp"
                    android:layout_marginEnd="3dp"
                    android:background="@drawable/btn_manage_edit"
                    android:text="修改"
                    android:textColor="@color/white"
                    android:textSize="6sp" />

                <Button
                    android:id="@+id/btnDelete"
                    android:layout_width="30dp"
                    android:layout_height="15dp"
                    android:background="@drawable/btn_manage_delete"
                    android:text="刪除"
                    android:textColor="@color/white"
                    android:textSize="6sp" />
            </LinearLayout>

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/colorBlueLine" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/colorBlueLine" />
    </LinearLayout>
</layout>