<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>


    </data>

    <LinearLayout
        android:layout_width="260dp"
        android:layout_height="310dp"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="28dp"
            android:background="@color/colorBlueLine"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="11sp"
            tools:text="操作" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:orientation="horizontal"
            android:paddingStart="6dp"
            android:paddingEnd="6dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="标题："
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/etName"
                android:layout_width="match_parent"
                android:layout_height="22dp"
                android:background="@drawable/bg_edit_blue"
                android:imeOptions="actionDone"
                android:paddingStart="3dp"
                android:paddingEnd="3dp"
                android:singleLine="true"
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:orientation="horizontal"
            android:paddingStart="6dp"
            android:paddingEnd="6dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="类别："
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/tvType"
                android:layout_width="match_parent"
                android:layout_height="22dp"
                android:background="@drawable/bg_edit_blue"
                android:gravity="center_vertical"
                android:paddingStart="3dp"
                android:paddingEnd="3dp"
                android:singleLine="true"
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_marginTop="6dp"
            android:orientation="horizontal"
            android:paddingStart="6dp"
            android:paddingEnd="6dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="封面："
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />

            <ImageView
                android:id="@+id/ivTemp"
                android:layout_width="100dp"
                android:layout_height="70dp"
                android:src="@drawable/bg_upload" />

            <Button
                android:id="@+id/btnSelectImage"
                android:layout_width="45dp"
                android:layout_height="20dp"
                android:layout_marginStart="15dp"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/btn_manage_edit"
                android:text="选择图片"
                android:textColor="@color/white"
                android:textSize="7sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="6dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:paddingStart="6dp"
            android:paddingEnd="6dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="内容："
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/etContent"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_edit_blue"
                android:gravity="top"
                android:imeOptions="actionDone"
                android:lineSpacingMultiplier="1.3"
                android:padding="3dp"
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnConfirm"
                android:layout_width="65dp"
                android:layout_height="25dp"
                android:layout_margin="10dp"
                android:background="@drawable/btn_manage_edit"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="9sp" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnCancel"
                android:layout_width="65dp"
                android:layout_height="25dp"
                android:layout_margin="10dp"
                android:background="@drawable/btn_manage_edit"
                android:text="取消"
                android:textColor="@color/white"
                android:textSize="9sp" />
        </LinearLayout>
    </LinearLayout>
</layout>