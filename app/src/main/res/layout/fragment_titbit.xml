<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>


    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical"
        android:padding="5dp"
        tools:context="com.ygxj.self.ui.back.titbit.TitbitFragment">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingTop="5dp"
            android:paddingBottom="5dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:focusable="true"
                android:focusableInTouchMode="true" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="活动名称："
                android:textColor="@color/lightBlack"
                android:textSize="9sp" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/etSearch"
                android:layout_width="0dp"
                android:layout_weight="1.5"
                android:layout_height="22dp"
                android:background="@drawable/bg_edit_blue"
                android:paddingStart="3dp"
                android:paddingEnd="3dp"
                android:singleLine="true"
                android:textColor="@color/lightBlack"
                android:textSize="9sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="录入时间："
                android:textColor="@color/lightBlack"
                android:textSize="9sp" />

            <TextView
                android:id="@+id/tvStartTime"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="20dp"
                android:background="@drawable/bg_edit_blue"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/lightBlack"
                android:textSize="9sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="3dp"
                android:text="-"
                android:textColor="@color/lightBlack"
                android:textSize="11sp" />

            <TextView
                android:id="@+id/tvEndTime"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="20dp"
                android:background="@drawable/bg_edit_blue"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/lightBlack"
                android:textSize="9sp" />

            <Button
                android:id="@+id/btnSearch"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="20dp"
                android:layout_marginStart="5dp"
                android:background="@drawable/btn_manage_edit"
                android:text="搜索"
                android:textColor="@color/white"
                android:textSize="8sp" />

            <Button
                android:id="@+id/btnReset"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="20dp"
                android:layout_marginStart="5dp"
                android:background="@drawable/btn_manage_edit"
                android:text="重置"
                android:textColor="@color/white"
                android:textSize="8sp" />

            <Button
                android:id="@+id/btnAdd"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="20dp"
                android:layout_marginStart="5dp"
                android:background="@drawable/btn_manage_edit"
                android:text="添加"
                android:textColor="@color/white"
                android:textSize="8sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="25dp"
            android:background="@color/colorBlueLine"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/colorBlueLine" />

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="序号"
                android:textColor="@color/white"
                android:textSize="9sp" />

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/white" />

            <TextView
                android:layout_width="150dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="活动名称"
                android:textColor="@color/white"
                android:textSize="9sp" />

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/white" />

            <TextView
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="录入时间"
                android:textColor="@color/white"
                android:textSize="9sp" />

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="center"
                android:text="排序"
                android:textColor="@color/white"
                android:textSize="9sp" />

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:gravity="center"
                android:text="操作"
                android:textColor="@color/white"
                android:textSize="9sp" />

            <View
                android:layout_width="1px"
                android:layout_height="match_parent"
                android:background="@color/colorBlueLine" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="3"
            tools:listitem="@layout/item_titbit_manage" />
    </LinearLayout>
</layout>