<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android">

    <data />

    <LinearLayout
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/common_primary_color"
            android:padding="8dp"
            android:text="修改密码"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="end"
            android:orientation="horizontal"
            android:paddingStart="6dp"
            android:paddingEnd="6dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="原始密码"
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />

            <EditText
                android:id="@+id/etOldPwd"
                android:layout_width="140dp"
                android:layout_height="22dp"
                android:layout_marginStart="5dp"
                android:background="@drawable/bg_edit_blue"
                android:inputType="textPassword"
                android:paddingStart="3dp"
                android:paddingEnd="3dp"
                android:singleLine="true"
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="end"
            android:orientation="horizontal"
            android:paddingStart="6dp"
            android:paddingEnd="6dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="新密码"
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />

            <EditText
                android:id="@+id/etNewPwd"
                android:layout_width="140dp"
                android:layout_height="22dp"
                android:layout_marginStart="5dp"
                android:background="@drawable/bg_edit_blue"
                android:inputType="textPassword"
                android:paddingStart="3dp"
                android:paddingEnd="3dp"
                android:singleLine="true"
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="end"
            android:orientation="horizontal"
            android:paddingStart="6dp"
            android:paddingEnd="6dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="确认密码"
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />

            <EditText
                android:id="@+id/etNewPwdConfirm"
                android:layout_width="140dp"
                android:layout_height="22dp"
                android:layout_marginStart="5dp"
                android:background="@drawable/bg_edit_blue"
                android:inputType="textPassword"
                android:paddingStart="3dp"
                android:paddingEnd="3dp"
                android:singleLine="true"
                android:textColor="@color/lightBlack"
                android:textSize="10sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnConfirm"
                android:layout_width="60dp"
                android:layout_height="20dp"
                android:layout_margin="10dp"
                android:background="@drawable/btn_manage_edit"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="8sp" />

            <Button
                android:id="@+id/btnCancel"
                android:layout_width="60dp"
                android:layout_height="20dp"
                android:layout_margin="10dp"
                android:background="@drawable/btn_manage_edit"
                android:text="取消"
                android:textColor="@color/white"
                android:textSize="8sp" />
        </LinearLayout>


    </LinearLayout>
</layout>