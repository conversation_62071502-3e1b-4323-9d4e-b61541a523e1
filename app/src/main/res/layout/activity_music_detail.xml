<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.main.soul.RelaxMusicActivity">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_login"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/ivBubble"
            android:layout_width="315dp"
            android:layout_height="360dp"
            android:scaleType="fitXY"
            android:src="@drawable/bg_bubble"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <FrameLayout
            android:id="@+id/flTitleBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@id/llPlayer"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="心理音乐"
                android:textColor="@color/white"
                android:textSize="17sp"
                tools:ignore="HardcodedText" />

        </FrameLayout>

        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:src="@drawable/ic_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <LinearLayout
            android:id="@+id/llPlayer"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.8"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.7"
            app:layout_constraintWidth_percent="0.8">

            <TextView
                android:id="@+id/tvMusicTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="15sp"
                tools:text="音乐标题" />

            <RelativeLayout
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_marginBottom="10dp">

                <RelativeLayout
                    android:id="@+id/rlMusicCover"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:layout_margin="20dp"
                        android:background="@drawable/bg_music_vinyl" />

                    <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
                        android:id="@+id/ivMusicPicture"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:layout_margin="50dp"
                        android:scaleType="centerCrop"
                        app:qmui_border_color="@color/white"
                        app:qmui_border_width="0dp"
                        app:qmui_is_circle="true"
                        tools:src="@drawable/placeholder" />
                </RelativeLayout>

                <ImageView
                    android:id="@+id/ivVinylPlay"
                    android:layout_width="30dp"
                    android:layout_height="80dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="10dp"
                    android:src="@drawable/ic_music_vinyl_play" />

            </RelativeLayout>

            <com.hjq.shape.layout.ShapeLinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="10dp"
                app:shape_radius="5dp"
                app:shape_solidColor="#80000000">

                <ImageView
                    android:id="@+id/ivPre"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_margin="5dp"
                    android:src="@drawable/ic_music_previous" />

                <ImageView
                    android:id="@+id/ivPlay"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_margin="5dp"
                    android:src="@drawable/selector_music_play" />

                <ImageView
                    android:id="@+id/ivNext"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_margin="5dp"
                    android:src="@drawable/ic_music_next" />

                <TextView
                    android:id="@+id/tvProgress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:textColor="@color/white"
                    android:textSize="11sp"
                    tools:text="00:00" />

                <SeekBar
                    android:id="@+id/seekBar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:focusable="true"
                    android:maxHeight="3dp"
                    android:minHeight="3dp"
                    android:progressDrawable="@drawable/music_progress"
                    android:thumbTint="@color/white" />

                <TextView
                    android:id="@+id/tvDuration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:textColor="@color/white"
                    android:textSize="11sp"
                    tools:text="00:00" />

                <ImageView
                    android:id="@+id/ivMode"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_margin="5dp"
                    android:background="@drawable/ic_music_order_play" />

                <ImageView
                    android:id="@+id/ivList"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_margin="5dp"
                    android:src="@drawable/ic_music_list" />
            </com.hjq.shape.layout.ShapeLinearLayout>
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rlCover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="160dp"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="60dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/bg_music_list"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="5dp"
                        android:textColor="#CCCCCC"
                        android:textSize="10sp"
                        tools:text="播放列表" />

                    <ImageView
                        android:id="@+id/ivClose"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:padding="6dp"
                        android:src="@drawable/ic_music_close" />
                </RelativeLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:overScrollMode="never"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_relax_music" />
            </LinearLayout>
        </RelativeLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>