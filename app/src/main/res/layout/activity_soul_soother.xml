<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clRoot"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_login"
        tools:context=".ui.main.soul.SoulSootherActivity">

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/ivBubble"
            android:layout_width="315dp"
            android:layout_height="360dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:src="@drawable/bg_bubble" />

        <FrameLayout
            android:id="@+id/flTitleBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="心灵鸡汤"
                android:textColor="@color/white"
                android:textSize="17sp"
                tools:ignore="HardcodedText" />

        </FrameLayout>

        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:src="@drawable/ic_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <com.hjq.shape.layout.ShapeLinearLayout
            android:id="@+id/llContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:orientation="vertical"
            android:paddingHorizontal="15dp"
            android:paddingVertical="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shape_radius="3dp"
            app:shape_solidColor="#503e8fc7"
            app:shape_strokeColor="#60a7db"
            app:shape_strokeSize="0.5dp">

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/ibMovie"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:background="@drawable/btn_soul_item1" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/ibMusic"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginVertical="10dp"
                android:background="@drawable/btn_soul_item2" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/ibPicture"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:background="@drawable/btn_soul_item3" />
        </com.hjq.shape.layout.ShapeLinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="@+id/llContent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/llContent"
            app:layout_constraintTop_toTopOf="@+id/llContent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:orientation="horizontal"
                app:layoutManager="com.google.android.flexbox.FlexboxLayoutManager"
                app:spanCount="2"
                tools:itemCount="30"
                tools:listitem="@layout/item_soul_soother" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvNum"
            android:layout_width="100dp"
            android:layout_height="25dp"
            android:background="@drawable/bg_psy_num"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="10sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/llContent"
            tools:text='共10条' />


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>