<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:background="@drawable/bg_login"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="com.ygxj.self.ui.login.LoginActivity">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="117dp"
            android:layout_height="30dp"
            android:layout_marginStart="15dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/ic_logo" />

        <LinearLayout
            android:layout_width="314dp"
            android:layout_height="220dp"
            android:layout_centerInParent="true"
            android:background="@drawable/bg_login_main"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/app_name"
                android:textColor="#00a8eb"
                android:textSize="20sp" />

            <com.hjq.shape.layout.ShapeLinearLayout
                android:layout_width="180dp"
                android:layout_height="28dp"
                app:shape_strokeSize="0.5dp"
                app:shape_strokeColor="@color/common_primary_color"
                app:shape_solidColor="@color/white"
                android:layout_marginTop="8dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="2dp">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    android:src="@drawable/ic_baseline_person" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/etAccount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:inputType="text"
                    android:hint="请输入账号"
                    android:singleLine="true"
                    android:textColor="@color/lightBlack"
                    android:textColorHint="@color/lightestBlack"
                    android:textSize="12sp" />

            </com.hjq.shape.layout.ShapeLinearLayout>

            <com.hjq.shape.layout.ShapeLinearLayout
                app:shape_strokeSize="0.5dp"
                app:shape_strokeColor="@color/common_primary_color"
                app:shape_solidColor="@color/white"
                android:layout_width="180dp"
                android:layout_height="28dp"
                android:layout_marginTop="8dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="2dp">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    android:src="@drawable/ic_baseline_lock" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/etPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:inputType="textPassword"
                    android:singleLine="true"
                    android:hint="请输入密码"
                    android:textColor="@color/lightBlack"
                    android:textColorHint="@color/lightestBlack"
                    android:textSize="12sp" />

            </com.hjq.shape.layout.ShapeLinearLayout>

            <LinearLayout
                android:layout_width="190dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp">

                <CheckBox
                    android:id="@+id/cbRemember"
                    android:text="记住密码"
                    android:drawablePadding="0dp"
                    android:padding="0dp"
                    android:minHeight="0dp"
                    android:minWidth="0dp"
                    android:textColor="@color/common_primary_color"
                    android:textSize="11sp"
                    android:button="@drawable/selector_box"
                    android:buttonTint="@color/common_primary_color"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <com.ygxj.self.other.PressAlphaButton
                    android:id="@+id/btnLogin"
                    android:layout_width="80dp"
                    android:layout_height="26dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/btn_login_blue"
                    android:text="登录"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <com.ygxj.self.other.PressAlphaButton
                    android:id="@+id/btnCancel"
                    android:gravity="center"
                    android:layout_width="80dp"
                    android:layout_height="26dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/btn_login_blue"
                    android:text="取消"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>

    </RelativeLayout>
</layout>