<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <!--        <variable-->
        <!--            name="m"-->
        <!--            type="com.ygxj.psht.data.entity.ScaleEntity" />-->
    </data>

    <RelativeLayout
        android:id="@+id/item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black">

        <com.luck.picture.lib.photoview.PhotoView
            android:id="@+id/photoView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            tools:text="标题"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tvNum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_margin="10dp"
            android:text="0/0"
            android:textColor="@color/white" />

        <ProgressBar
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />
    </RelativeLayout>
</layout>