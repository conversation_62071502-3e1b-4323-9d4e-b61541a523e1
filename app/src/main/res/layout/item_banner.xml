<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_banner"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            tools:ignore="ContentDescription" />

        <View
            android:id="@+id/v_mask"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#80000000" />

        <ImageView
            android:id="@+id/iv_play"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_centerInParent="true"
            android:src="@drawable/ic_play" />

    </RelativeLayout>
</layout>