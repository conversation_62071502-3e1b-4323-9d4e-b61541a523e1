<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="m"
            type="com.ygxj.self.data.entity.TitbitEntity" />
    </data>

    <RelativeLayout
        android:id="@+id/item"
        android:layout_width="match_parent"
        android:layout_height="35dp">

        <com.hjq.shape.view.ShapeTextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="3dp"
            android:ellipsize="end"
            android:gravity="center"
            android:paddingHorizontal="2dp"
            android:maxLines="2"
            android:textColor="#FFFFFF"
            android:textSize="10sp"
            app:shape_radius="5dp"
            selected="@{m.isSelected}"
            app:shape_solidColor="#501b1c1c"
            app:shape_solidSelectedColor="#90134c78"
            app:shape_strokeColor="@color/white"
            app:shape_strokeSize="1dp"
            android:text="@{m.title}"
            tools:text="这是一个很寂寞的天" />

    </RelativeLayout>
</layout>