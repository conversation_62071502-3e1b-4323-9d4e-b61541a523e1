<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="5dp"
            android:layout_marginBottom="5dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="75:100"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@tools:sample/backgrounds/scenic" />

        <LinearLayout
            android:id="@+id/ll_mask"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#80000000"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
            app:layout_constraintEnd_toEndOf="@+id/iv_cover"
            app:layout_constraintStart_toStartOf="@+id/iv_cover"
            app:layout_constraintTop_toTopOf="@+id/iv_cover">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_play"
                app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
                app:layout_constraintEnd_toEndOf="@+id/iv_cover"
                app:layout_constraintStart_toStartOf="@+id/iv_cover"
                app:layout_constraintTop_toTopOf="@+id/iv_cover" />
        </LinearLayout>


        <com.ygxj.self.other.PressAlphaImageView
            android:id="@+id/iv_delete"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_delete"
            app:layout_constraintEnd_toEndOf="@+id/iv_cover"
            app:layout_constraintTop_toTopOf="@+id/iv_cover" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>