<?xml version="1.0" encoding="utf-8"?>
<animation-list xmlns:android="http://schemas.android.com/apk/res/android"
    android:oneshot="false"> <!-- false 表示动画循环 -->

    <item android:drawable="@mipmap/q1" android:duration="200" />
    <item android:drawable="@mipmap/q2" android:duration="200" />
    <item android:drawable="@mipmap/q3" android:duration="200" />
    <item android:drawable="@mipmap/q4" android:duration="200" />
    <item android:drawable="@mipmap/q5" android:duration="200" />
    <item android:drawable="@mipmap/q6" android:duration="200" />
    <item android:drawable="@mipmap/q7" android:duration="200" />
    <item android:drawable="@mipmap/q8" android:duration="200" />
    <item android:drawable="@mipmap/q9" android:duration="200" />
    <item android:drawable="@mipmap/q10" android:duration="200" />
    <!-- 添加更多帧 -->

</animation-list>