<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA"
        tools:ignore="PermissionImpliesUnsupportedChromeOsHardware" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <application
        android:name="com.ygxj.self.AppApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/SplashTheme"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <meta-data
            android:name="design_width_in_dp"
            android:value="640" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="360" />

        <!-- 表示当前已经适配了分区存储 -->
        <meta-data
            android:name="ScopedStorage"
            android:value="true" />

        <!-- 闪屏页 -->
        <activity
            android:name="com.ygxj.self.ui.splash.SplashActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/SplashTheme"
            tools:ignore="LockedOrientationActivity">

            <!-- 程序入口 -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 登录页 -->
        <activity
            android:name="com.ygxj.self.ui.login.LoginActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape"
            android:windowSoftInputMode="adjustPan" />

        <!-- 主页 -->
        <activity
            android:name="com.ygxj.self.ui.main.MainActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 中心介绍 -->
        <activity
            android:name="com.ygxj.self.ui.center.CenterIntroActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 中心活动花絮 -->
        <activity
            android:name="com.ygxj.self.ui.titbit.TitbitActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 你所不知道的心理学 -->
        <activity
            android:name="com.ygxj.self.ui.psychology.PsychologyActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 你所不知道的心理学详情 -->
        <activity
            android:name="com.ygxj.self.ui.psychology.PsychologyDetailActivity"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />

        <!-- 心理鸡汤 -->
        <activity
            android:name="com.ygxj.self.ui.soul.SoulSootherActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 视频播放 -->
        <activity
            android:name="com.ygxj.self.ui.soul.movie.MovieDetailActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 音乐播放 -->
        <activity
            android:name="com.ygxj.self.ui.soul.music.MusicDetailActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 图片播放 -->
        <activity
            android:name="com.ygxj.self.ui.soul.image.ImageDetailActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 图片预览 -->
        <activity
            android:name="com.ygxj.self.ui.image.ImagePreviewActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 后台主页面 -->
        <activity
            android:name="com.ygxj.self.ui.back.BackgroundActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustPan"
            android:screenOrientation="sensorLandscape" />

        <!-- 修改配置 -->
        <activity
            android:name="com.ygxj.self.ui.config.ConfigActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 心理加油站 -->
        <activity
            android:name="com.ygxj.self.ui.gas.GasStationActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

    </application>

    <queries package="${applicationId}">
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE">

            </action>
        </intent>
        <intent>
            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE">

            </action>
        </intent>
    </queries>
</manifest>